<header class="bg-white shadow-md fixed w-full z-50">
               <nav class="container mx-auto px-6 py-4">
                   <div class="flex justify-between items-center">
                       <!-- Logo -->
                       <div class="flex items-center">
                           <a href="index.html" class="flex items-center space-x-3">
                               <!-- Logo Mark -->
                               <div class="relative w-10 h-10">
                                   <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg transform rotate-3 transition-transform group-hover:rotate-6"></div>
                                   <div class="absolute inset-0 bg-white rounded-lg transform -rotate-3 transition-transform group-hover:-rotate-6">
                                       <div class="absolute inset-2 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-md flex items-center justify-center">
                                           <span class="text-white text-xl font-bold">P</span>
                                       </div>
                                   </div>
                               </div>
                               <!-- Logo Type -->
                               <div class="flex flex-col">
                                   <span class="text-xl font-bold text-gray-900">Pitor</span>
                                   <span class="text-sm text-gray-600">Digital Solutions</span>
                               </div>
                           </a>
                       </div>
                       
                       <!-- Desktop Menu -->
                       <div class="hidden md:flex items-center space-x-8">
                           <a href="index.html" class="text-gray-700 hover:text-blue-600">Home</a>
                           <a href="products.html" class="text-gray-700 hover:text-blue-600">Products</a>
                           <!-- <a href="#services" class="text-gray-700 hover:text-blue-600">Services</a> -->
                           <a href="about.html" class="text-gray-700 hover:text-blue-600">About</a>
                           <!-- <a href="/contact.html" class="text-gray-700 hover:text-blue-600">Contact</a> -->
                           <a href="login.html" class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                               <i class="fas fa-sign-in-alt"></i>
                               <span>Login</span>
                           </a>
                       </div>
           
                       <!-- Mobile Menu Button -->
                       <div class="md:hidden flex items-center space-x-4">
                           <a href="login.html" class="text-gray-700 hover:text-blue-600">
                               <i class="fas fa-sign-in-alt text-xl"></i>
                           </a>
                           <button id="mobile-menu-button" class="text-gray-700 hover:text-blue-600 focus:outline-none">
                               <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                               </svg>
                           </button>
                       </div>
                   </div>
               </nav>
           
               <!-- Mobile Menu (Hidden by Default) -->
               <div id="mobile-menu" class="md:hidden hidden bg-white shadow-md">
                   <div class="container mx-auto px-6 py-4">
                       <div class="space-y-3">
                           <a href="index.html" class="block text-gray-700 hover:text-blue-600">Home</a>
                           <a href="products.html" class="block text-gray-700 hover:text-blue-600">Products</a>
                           <a href="about.html" class="block text-gray-700 hover:text-blue-600">About</a>
                           <a href="login.html" class="block text-gray-700 hover:text-blue-600">Login</a>
                       </div>
                   </div>
               </div>
           </header>