<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pitor - Digital Solutions Provider</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="icon" type="image/png" href="images/favicon.png">

    <!-- Custom Styles -->
    <style>
        /* Animation Delay Utilities */
        .animation-delay-300 { animation-delay: 300ms; }
        .animation-delay-500 { animation-delay: 500ms; }
        .animation-delay-700 { animation-delay: 700ms; }
        .animation-delay-1000 { animation-delay: 1000ms; }
    </style>
    <!-- Social Media Sharing -->
    <meta property="og:image" content="images/social-share-image.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta name="twitter:image" content="images/social-share-image.png">
    <link rel="image_src" href="images/social-share-image.png">
</head>
<body>

    <!-- Header -->
    <header class="bg-white shadow-md fixed w-full z-50">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-3">
                        <!-- Logo Mark -->
                        <img src="images/Logo.png" alt="Pitor Logo" class="w-18 h-10">
                        <!-- Logo Type -->

                    </a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-blue-600">Home</a>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Services</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-laptop-code text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Web Development</div>
                                        <div class="text-sm text-gray-600">Custom website solutions</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-mobile-alt text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">Mobile Development</div>
                                        <div class="text-sm text-gray-600">iOS and Android applications</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-paint-brush text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">UI/UX Design</div>
                                        <div class="text-sm text-gray-600">User interface and experience</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-bullhorn text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Digital Marketing</div>
                                        <div class="text-sm text-gray-600">SEO and online advertising</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-comments text-red-600"></i>
                                    <div>
                                        <div class="font-semibold">IT Consulting</div>
                                        <div class="text-sm text-gray-600">Expert technology advice</div>
                                    </div>
                                </a>
                                <div class="border-t border-gray-100 my-2"></div>
                                <a href="#services" class="flex items-center justify-between text-sm text-blue-600 hover:text-blue-700 p-2">
                                    <span>View All Services</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Solutions</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="e-commerce-solution.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-shopping-cart text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">E-Commerce Solution</div>
                                        <div class="text-sm text-gray-600">Complete online store management</div>
                                    </div>
                                </a>
                                <a href="crm-system.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-users text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">CRM System</div>
                                        <div class="text-sm text-gray-600">Customer relationship management</div>
                                    </div>
                                </a>
                                <a href="pos-system.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-cash-register text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">POS System</div>
                                        <div class="text-sm text-gray-600">Point of sale management</div>
                                    </div>
                                </a>
                                <a href="payment-gateway.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-credit-card text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Payment Gateway</div>
                                        <div class="text-sm text-gray-600">Secure payment processing</div>
                                    </div>
                                </a>

                                <a href="flight-booking.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-credit-card text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Flight Booking</div>
                                        <div class="text-sm text-gray-600">Secure Flight Ticket processing</div>
                                    </div>
                                </a>


                                <div class="border-t border-gray-100 my-2"></div>
                                <a href="solutions.html" class="flex items-center justify-between text-sm text-blue-600 hover:text-blue-700 p-2">
                                    <span>View All Solutions</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>




                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Products</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="products.html?category=Management%20Systems" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-tasks text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Management Systems</div>
                                        <div class="text-sm text-gray-600">Business process automation</div>
                                    </div>
                                </a>
                                <a href="products.html?category=Financial%20Solutions" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-chart-line text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">Financial Solutions</div>
                                        <div class="text-sm text-gray-600">Payment and accounting tools</div>
                                    </div>
                                </a>
                                <a href="products.html?category=Booking%20Solutions" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-calendar-check text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">Booking Solutions</div>
                                        <div class="text-sm text-gray-600">Reservation and scheduling</div>
                                    </div>
                                </a>
                                <a href="products.html?category=Mobile%20Applications" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-mobile-alt text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Mobile Applications</div>
                                        <div class="text-sm text-gray-600">iOS and Android solutions</div>
                                    </div>
                                </a>
                                <a href="products.html?category=E-Commerce" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-shopping-cart text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">E-Commerce Solutions</div>
                                        <div class="text-sm text-gray-600">Online store management</div>
                                    </div>
                                </a>

                                <div class="border-t border-gray-100 my-2"></div>
                                <a href="products.html" class="flex items-center justify-between text-sm text-blue-600 hover:text-blue-700 p-2">
                                    <span>View All Products</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>

                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Hosting</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-server text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Shared Hosting</div>
                                        <div class="text-sm text-gray-600">Affordable hosting solutions</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-server text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">VPS Hosting</div>
                                        <div class="text-sm text-gray-600">Virtual private server hosting</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-hdd text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">Dedicated Servers</div>
                                        <div class="text-sm text-gray-600">Full server control and resources</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-cloud text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Cloud Hosting</div>
                                        <div class="text-sm text-gray-600">Scalable cloud infrastructure</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-globe text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Domain Name Registration</div>
                                        <div class="text-sm text-gray-600">Secure your online identity</div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Company</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="about.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-info-circle text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">About</div>
                                        <div class="text-sm text-gray-600">Learn about our company</div>
                                    </div>
                                </a>
                                <a href="contact.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-envelope text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">Contact Us</div>
                                        <div class="text-sm text-gray-600">Get in touch with us</div>
                                    </div>
                                </a>
                                <a href="careers.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-briefcase text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">Careers</div>
                                        <div class="text-sm text-gray-600">Join our team</div>
                                    </div>
                                </a>
                                <a href="about.html#team" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-users text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Our Team</div>
                                        <div class="text-sm text-gray-600">Meet our experts</div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                    <a href="login.html" class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Login</span>
                    </a>
                </div>

                <!-- Mobile Menu Button -->
                <div class="md:hidden flex items-center space-x-4">
                    <a href="login.html" class="text-gray-700 hover:text-blue-600">
                        <i class="fas fa-sign-in-alt text-xl"></i>
                    </a>
                    <button id="mobile-menu-button" class="text-gray-700 hover:text-blue-600 focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Mobile Menu (Hidden by Default) -->
        <div id="mobile-menu" class="md:hidden hidden bg-white shadow-md">
            <div class="container mx-auto px-6 py-4">
                <a href="index.html" class="block text-gray-700 hover:text-blue-600 py-2">Home</a>
                <!-- Services Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Services</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">Web Development</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">Mobile Development</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">UI/UX Design</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">Digital Marketing</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">IT Consulting</a>
                        <a href="#services" class="block text-blue-600 hover:text-blue-700 py-2">View All Services</a>
                    </div>
                </div>
                <!-- Add Solutions Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Solutions</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="e-commerce-solution.html" class="block text-gray-700 hover:text-blue-600 py-2">E-Commerce Solution</a>
                        <a href="crm-system.html" class="block text-gray-700 hover:text-blue-600 py-2">CRM System</a>
                        <a href="pos-system.html" class="block text-gray-700 hover:text-blue-600 py-2">POS System</a>
                        <a href="payment-gateway.html" class="block text-gray-700 hover:text-blue-600 py-2">Payment Gateway</a>
                        <a href="solutions.html" class="block text-blue-600 hover:text-blue-700 py-2">View All Solutions</a>
                    </div>
                </div>
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Products</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="products.html?category=Management%20Systems" class="block text-gray-700 hover:text-blue-600 py-2">Management Systems</a>
                        <a href="products.html?category=Financial%20Solutions" class="block text-gray-700 hover:text-blue-600 py-2">Financial Solutions</a>
                        <a href="products.html?category=Booking%20Solutions" class="block text-gray-700 hover:text-blue-600 py-2">Booking Solutions</a>
                        <a href="products.html?category=Mobile%20Applications" class="block text-gray-700 hover:text-blue-600 py-2">Mobile Applications</a>
                        <a href="products.html?category=E-Commerce" class="block text-gray-700 hover:text-blue-600 py-2">E-Commerce Solutions</a>
                        <a href="products.html" class="block text-blue-600 hover:text-blue-700 py-2">View All Products</a>
                    </div>
                </div>
                <!-- Add Hosting Services Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Hosting</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Shared Hosting</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">VPS Hosting</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Dedicated Servers</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Cloud Hosting</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Domain Name Registration</a>
                    </div>
                </div>
                <a href="services.html" class="block text-gray-700 hover:text-blue-600 py-2">Services</a>
                <!-- Add Company Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Company</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="about.html" class="block text-gray-700 hover:text-blue-600 py-2">About</a>
                        <a href="contact.html" class="block text-gray-700 hover:text-blue-600 py-2">Contact Us</a>
                        <a href="careers.html" class="block text-gray-700 hover:text-blue-600 py-2">Careers</a>
                        <a href="about.html#team" class="block text-gray-700 hover:text-blue-600 py-2">Our Team</a>
                    </div>
                </div>
                <a href="login.html" class="block text-gray-700 hover:text-blue-600 py-2">Login</a>
            </div>
        </div>
    </header>



    <!-- Hero Section -->
    <section class="pt-24 bg-gradient-to-br from-blue-50 to-indigo-50 overflow-hidden">
        <div class="container mx-auto px-6 py-20 relative">
            <!-- Animated Background Elements -->
            <div class="absolute inset-0 overflow-hidden">
                <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full opacity-20 animate-pulse"></div>
                <div class="absolute top-60 -left-20 w-60 h-60 bg-indigo-100 rounded-full opacity-20 animate-pulse delay-150"></div>
                <div class="absolute -bottom-40 right-20 w-40 h-40 bg-purple-100 rounded-full opacity-20 animate-pulse delay-300"></div>
            </div>

            <!-- Grid Container -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center relative z-10">
                <!-- Left Content -->
                <div class="relative z-10 space-y-8">
                    <div class="inline-block">
                        <div class="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
                            <span class="animate-pulse relative flex h-3 w-3">
                                <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                                <span class="relative inline-flex rounded-full h-3 w-3 bg-blue-500"></span>
                            </span>
                            <span class="text-gray-600">Leading Tech Solutions Provider</span>
                        </div>
                    </div>

                    <h1 class="text-5xl font-bold mb-6 leading-tight">
                        Transform Your Business With
                        <span class="relative">
                            <span class="text-blue-600">Pitor</span>
                            <svg class="absolute -bottom-2 left-0 w-full" height="10" viewBox="0 0 100 10" preserveAspectRatio="none">
                                <path d="M0 5 Q 25 0, 50 5 Q 75 10, 100 5" stroke="currentColor" stroke-width="2" fill="none" class="text-blue-200"/>
                            </svg>
                        </span>
                    </h1>

                    <p class="text-gray-600 text-lg mb-8 leading-relaxed">
                        We provide cutting-edge software solutions that help businesses grow and succeed in the digital age.
                    </p>

                    <!-- Feature Cards -->
                    <div class="grid grid-cols-2 gap-4 mb-8">
                        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 transform hover:scale-105 transition-transform duration-300">
                            <div class="flex items-center space-x-3">
                                <div class="bg-blue-100 rounded-lg p-2">
                                    <i class="fas fa-rocket text-blue-600 text-xl"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold">Fast Development</h4>
                                    <p class="text-sm text-gray-600">Quick Time to Market</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 transform hover:scale-105 transition-transform duration-300">
                            <div class="flex items-center space-x-3">
                                <div class="bg-indigo-100 rounded-lg p-2">
                                    <i class="fas fa-shield-alt text-indigo-600 text-xl"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold">Secure Solutions</h4>
                                    <p class="text-sm text-gray-600">Enterprise-Grade Security</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="flex flex-wrap gap-4">
                        <a href="#products" class="group relative inline-flex items-center px-8 py-4 rounded-lg bg-blue-600 text-white overflow-hidden">
                            <span class="absolute left-0 w-full h-full bg-blue-700 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-300"></span>
                            <span class="relative flex items-center">
                                Our Products
                                <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform"></i>
                            </span>
                        </a>
                        <a href="#about" class="group relative inline-flex items-center px-8 py-4 rounded-lg bg-white text-gray-800 overflow-hidden hover:shadow-lg transition-shadow">
                            <span class="relative flex items-center">
                                Learn More
                                <i class="fas fa-info-circle ml-2 transform group-hover:rotate-12 transition-transform"></i>
                            </span>
                        </a>
                    </div>
                </div>

                <!-- Right Content -->
                <div class="relative">
                    <!-- Main Image Container -->
                    <div class="relative z-10 bg-white rounded-2xl shadow-xl p-3 transform hover:scale-[1.02] transition-transform duration-500">
                        <div class="relative rounded-xl overflow-hidden">
                            <!-- Replace the image with a more professional tech/business image -->
                            <img src="https://images.unsplash.com/photo-1504384308090-c894fdcc538d?ixlib=rb-4.0.3"
                                 alt="Digital Solutions"
                                 class="w-full h-[500px] object-cover transform hover:scale-105 transition-transform duration-700">

                            <!-- Enhanced Gradient Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-tr from-blue-600/30 via-transparent to-indigo-600/30"></div>

                            <!-- Animated Tech Elements -->
                            <div class="absolute inset-0">
                                <!-- Floating Code Elements -->
                                <div class="absolute top-10 left-10 bg-white/90 backdrop-blur-sm rounded-lg p-3 transform hover:scale-110 transition-all duration-300 shadow-lg">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 rounded-full bg-red-500"></div>
                                        <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                                        <div class="w-3 h-3 rounded-full bg-green-500"></div>
                                    </div>
                                    <div class="mt-2 font-mono text-sm text-gray-600">
                                        <span class="text-blue-600">const</span> success = <span class="text-green-600">'guaranteed'</span>;
                                    </div>
                                </div>

                                <!-- Floating UI Elements -->
                                <div class="absolute top-1/4 right-10 bg-white/90 backdrop-blur-sm rounded-lg p-3 transform hover:scale-110 transition-all duration-300 shadow-lg">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-paint-brush text-purple-600"></i>
                                        <span class="text-sm font-semibold">UI/UX Design</span>
                                    </div>
                                </div>

                                <!-- Floating Mobile Elements -->
                                <div class="absolute bottom-20 left-10 bg-white/90 backdrop-blur-sm rounded-lg p-3 transform hover:scale-110 transition-all duration-300 shadow-lg">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-mobile-alt text-blue-600"></i>
                                        <span class="text-sm font-semibold">Mobile First</span>
                                    </div>
                                </div>

                                <!-- Tech Stack Pills -->
                                <div class="absolute bottom-10 right-10 flex flex-wrap gap-2 max-w-[300px]">
                                    <!-- Frontend -->
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-blue-600 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        React
                                    </div>
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-green-600 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        Vue.js
                                    </div>
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-orange-600 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        Angular
                                    </div>

                                    <!-- Backend -->
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-green-600 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        Node.js
                                    </div>
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-purple-600 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        PHP
                                    </div>
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-red-600 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        Laravel
                                    </div>

                                    <!-- Languages -->
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-yellow-600 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        Python
                                    </div>
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-blue-800 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        Java
                                    </div>
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-cyan-600 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        Go
                                    </div>

                                    <!-- Mobile -->
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-blue-500 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        Flutter
                                    </div>
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-gray-800 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        Swift
                                    </div>
                                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-semibold text-green-700 shadow-lg transform hover:scale-110 transition-all duration-300">
                                        Kotlin
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Floating Stats Cards -->
                        <div class="absolute -left-6 top-1/4 bg-white rounded-lg shadow-lg p-4 transform -translate-y-1/2 hover:scale-110 transition-transform">
                            <div class="flex items-center space-x-3">
                                <div class="bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full p-3">
                                    <i class="fas fa-code text-white text-xl"></i>
                                </div>
                                <div>
                                    <h4 class="font-bold">Clean Code</h4>
                                    <p class="text-sm text-gray-600">Best Practices</p>
                                </div>
                            </div>
                        </div>

                        <!-- Success Rate Card -->
                        <div class="absolute -right-6 bottom-1/4 bg-white rounded-lg shadow-lg p-4 transform hover:scale-110 transition-transform">
                            <div class="flex items-center space-x-3">
                                <div class="bg-gradient-to-r from-green-500 to-emerald-500 rounded-full p-3">
                                    <i class="fas fa-chart-line text-white text-xl"></i>
                                </div>
                                <div>
                                    <h4 class="font-bold text-lg">99.9%</h4>
                                    <p class="text-sm text-gray-600">Success Rate</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="absolute -bottom-10 -right-10 w-40 h-40 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full opacity-50 animate-pulse"></div>
                    <div class="absolute -top-10 -left-10 w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full opacity-50 animate-pulse delay-150"></div>

                    <!-- Animated Tech Stack Icons -->
                    <div class="absolute -right-4 top-1/2 transform -translate-y-1/2 space-y-4">
                        <!-- Cloud Services -->
                        <div class="bg-gradient-to-r from-orange-500 to-red-500 rounded-full p-3 shadow-lg transform hover:scale-110 transition-transform">
                            <i class="fab fa-aws text-white text-xl"></i>
                        </div>
                        <div class="bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full p-3 shadow-lg transform hover:scale-110 transition-transform">
                            <i class="fab fa-docker text-white text-xl"></i>
                        </div>
                        <!-- Kubernetes Icon -->
                        <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-full p-3 shadow-lg transform hover:scale-110 transition-transform">
                            <svg class="w-5 h-5 text-white" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15.9998 0L29.3278 7.7599V23.2796L15.9998 31.0395L2.67188 23.2796V7.7599L15.9998 0Z" fill="currentColor" fill-opacity="0.2"/>
                                <path d="M15.9998 2L27.3278 8.5599V21.6796L15.9998 28.2395L4.67188 21.6796V8.5599L15.9998 2Z" stroke="currentColor" stroke-width="1.5"/>
                                <path d="M16 7L22 10.5V17.5L16 21L10 17.5V10.5L16 7Z" fill="currentColor"/>
                            </svg>
                        </div>
                        <!-- Development Tools -->
                        <div class="bg-gradient-to-r from-gray-700 to-gray-900 rounded-full p-3 shadow-lg transform hover:scale-110 transition-transform">
                            <i class="fab fa-github text-white text-xl"></i>
                        </div>
                        <div class="bg-gradient-to-r from-red-500 to-red-700 rounded-full p-3 shadow-lg transform hover:scale-110 transition-transform">
                            <svg class="w-5 h-5 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L20 6V18L12 22L4 18V6L12 2Z" fill="currentColor"/>
                                <path d="M12 6L16 8V16L12 18L8 16V8L12 6Z" fill="white"/>
                            </svg>
                        </div>
                    </div>

                    <!-- Additional Floating Tech Elements -->
                    <div class="absolute top-1/3 left-10 bg-white/90 backdrop-blur-sm rounded-lg p-3 transform hover:scale-110 transition-all duration-300 shadow-lg">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-database text-indigo-600"></i>
                            <span class="text-sm font-semibold">MongoDB</span>
                        </div>
                    </div>

                    <div class="absolute top-2/3 right-10 bg-white/90 backdrop-blur-sm rounded-lg p-3 transform hover:scale-110 transition-all duration-300 shadow-lg">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-server text-red-600"></i>
                            <span class="text-sm font-semibold">Redis</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4 sm:px-6">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold mb-4">Featured Products</h2>
                <p class="text-gray-600">Discover our innovative solutions</p>
            </div>

            <!-- Product Grid - Will show random 8 products in 2 rows -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6" id="featuredProducts">
                <!-- Products will be dynamically inserted here -->
            </div>

            <!-- View All Button -->
            <div class="text-center mt-8 sm:mt-12">
                <a href="products.html"
                   class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300 group">
                    <span class="font-semibold">View All Products</span>
                    <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <script>
        // Product data
        const products = [
            {
                id: 1,
                title: "E-Commerce Solution",
                description: "Complete online store management system",
                image: "images/products/ecommerce.jpg",
                icon: "shopping-cart",
                gradient: "from-blue-600 to-indigo-600",
                tags: ["Payments", "Inventory", "Analytics"],
                tagColor: "blue",
                rating: "4.9",
                category: "Management Systems"
            },
            {
                id: 2,
                title: "CRM System",
                description: "Complete customer relationship management solution",
                image: "images/products/crm.jpg",
                icon: "users-cog",
                gradient: "from-green-600 to-emerald-600",
                tags: ["Lead Management", "Analytics", "Automation"],
                tagColor: "green",
                rating: "4.8",
                category: "Management Systems"
            },
            {
                id: 3,
                title: "POS System",
                description: "Modern point of sale and inventory management system",
                image: "images/products/pos.jpg",
                icon: "cash-register",
                gradient: "from-orange-600 to-red-600",
                tags: ["Sales", "Inventory", "Reports"],
                tagColor: "orange",
                rating: "4.7",
                category: "Management Systems"
            },
            {
                id: 4,
                title: "School Management",
                description: "Complete school administration and learning management solution",
                image: "images/products/school.jpg",
                icon: "school",
                gradient: "from-teal-600 to-emerald-600",
                tags: ["Attendance", "Grades", "Scheduling"],
                tagColor: "teal",
                rating: "4.8",
                category: "Management Systems"
            },
            {
                id: 5,
                title: "Hospital Management",
                description: "Complete hospital and healthcare management solution",
                image: "images/products/hospital.jpg",
                icon: "hospital",
                gradient: "from-rose-600 to-red-600",
                tags: ["Patient Records", "Appointments", "Billing"],
                tagColor: "rose",
                rating: "4.9",
                category: "Management Systems"
            },
            {
                id: 6,
                title: "Payment Gateway",
                description: "Secure and seamless payment processing solution",
                image: "images/products/payment.jpg",
                icon: "credit-card",
                gradient: "from-emerald-600 to-teal-600",
                tags: ["Multiple Gateways", "Fraud Protection", "Analytics"],
                tagColor: "emerald",
                rating: "4.9",
                category: "Financial Solutions"
            },
            {
                id: 7,
                title: "Mobile Banking",
                description: "Secure and user-friendly mobile banking platform",
                image: "images/products/mobile-banking.jpg",
                icon: "mobile-alt",
                gradient: "from-blue-600 to-cyan-600",
                tags: ["Fund Transfer", "Bill Payment", "Mobile Banking"],
                tagColor: "blue",
                rating: "4.9",
                category: "Financial Solutions"
            },
            {
                id: 8,
                title: "Mobile Recharge",
                description: "Complete mobile recharge and bill payment solution",
                image: "images/products/mobile-recharge.jpg",
                icon: "signal",
                gradient: "from-violet-600 to-purple-600",
                tags: ["Quick Recharge", "Multiple Operators", "Auto Pay"],
                tagColor: "violet",
                rating: "4.8",
                category: "Financial Solutions"
            },
            {
                id: 9,
                title: "Digital Wallet",
                description: "Secure digital wallet solution for modern payment needs",
                image: "images/products/digital-wallet.jpg",
                icon: "wallet",
                gradient: "from-fuchsia-600 to-pink-600",
                tags: ["P2P Transfer", "QR Payments", "Rewards"],
                tagColor: "fuchsia",
                rating: "4.9",
                category: "Financial Solutions"
            },
            {
                id: 10,
                title: "Currency Exchange",
                description: "Real-time currency exchange and trading platform",
                image: "images/products/currency-exchange.jpg",
                icon: "exchange-alt",
                gradient: "from-amber-600 to-yellow-600",
                tags: ["Live Rates", "Multi-Currency", "Analytics"],
                tagColor: "amber",
                rating: "4.8",
                category: "Financial Solutions"
            },
            {
                id: 11,
                title: "Flight Booking",
                description: "Complete flight reservation and management system",
                image: "images/products/flight-booking.jpg",
                icon: "plane",
                gradient: "from-sky-600 to-blue-600",
                tags: ["Multi-city Booking", "Seat Selection", "Price Alerts"],
                tagColor: "sky",
                rating: "4.9",
                category: "Booking Solutions"
            },
            {
                id: 12,
                title: "Blockchain Solutions",
                description: "Enterprise blockchain solutions and Web3 development services",
                image: "images/products/blockchain.jpg",
                icon: "cube",
                gradient: "from-purple-600 to-indigo-600",
                tags: ["Smart Contracts", "DeFi", "NFT"],
                tagColor: "purple",
                rating: "4.9",
                category: "Blockchain"
            }
        ];

        // Function to shuffle array
        function shuffleArray(array) {
            for (let i = array.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [array[i], array[j]] = [array[j], array[i]];
            }
            return array;
        }

        // Function to render a product card
        function renderProductCard(product) {
            return `
                <div class="group bg-white rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-1 transition-all duration-300">
                    <div class="relative h-48 sm:h-52 overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-r ${product.gradient} opacity-90"></div>
                        <img src="${product.image}"
                             alt="${product.title}"
                             class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-500">
                        <div class="absolute top-4 right-4 bg-white/20 backdrop-blur-sm p-2 rounded-full">
                            <i class="fas fa-${product.icon} text-xl text-white"></i>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="text-lg font-bold text-gray-900">${product.title}</h3>
                            <span class="flex items-center text-yellow-500">
                                <i class="fas fa-star"></i>
                                <span class="ml-1 text-xs">${product.rating}</span>
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">${product.description}</p>
                        <div class="flex flex-wrap gap-1 mb-3">
                            ${product.tags.map(tag => `
                                <span class="px-2 py-1 bg-${product.tagColor}-100 text-${product.tagColor}-600 rounded-full text-xs">${tag}</span>
                            `).join('')}
                        </div>
                        <a href="#" class="group inline-flex items-center text-sm text-${product.tagColor}-600 hover:text-${product.tagColor}-700">
                            <span class="font-semibold text-xs">Learn More</span>
                            <svg class="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </a>
                    </div>
                </div>
            `;
        }

        // Get random 8 products and render them
        const randomProducts = shuffleArray([...products]).slice(0, 8);
        const featuredProductsGrid = document.getElementById('featuredProducts');
        randomProducts.forEach(product => {
            featuredProductsGrid.innerHTML += renderProductCard(product);
        });

        // Store the products data in localStorage for the products page
        localStorage.setItem('allProducts', JSON.stringify(products));
    </script>

    <!-- Stats Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div class="p-6 bg-gray-50 rounded-lg">
                    <i class="fas fa-project-diagram text-3xl text-blue-600 mb-4"></i>
                    <h3 class="text-4xl font-bold text-blue-600">500+</h3>
                    <p class="text-gray-600">Projects Completed</p>
                </div>
                <div class="p-6 bg-gray-50 rounded-lg">
                    <i class="fas fa-smile-beam text-3xl text-blue-600 mb-4"></i>
                    <h3 class="text-4xl font-bold text-blue-600">200+</h3>
                    <p class="text-gray-600">Happy Clients</p>
                </div>
                <div class="p-6 bg-gray-50 rounded-lg">
                    <i class="fas fa-users text-3xl text-blue-600 mb-4"></i>
                    <h3 class="text-4xl font-bold text-blue-600">50+</h3>
                    <p class="text-gray-600">Team Members</p>
                </div>
                <div class="p-6 bg-gray-50 rounded-lg">
                    <i class="fas fa-calendar-check text-3xl text-blue-600 mb-4"></i>
                    <h3 class="text-4xl font-bold text-blue-600">10+</h3>
                    <p class="text-gray-600">Years Experience</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Technologies We're Expert In Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">Technologies We're Expert In</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Our team specializes in the latest technologies and frameworks to deliver cutting-edge solutions</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Frontend Technologies -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="text-blue-600 mb-6">
                        <i class="fas fa-laptop-code text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Frontend</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-react text-blue-500"></i>
                            <span>React.js</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-vuejs text-green-500"></i>
                            <span>Vue.js</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-angular text-red-500"></i>
                            <span>Angular</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-js text-yellow-500"></i>
                            <span>JavaScript/TypeScript</span>
                        </div>
                    </div>
                </div>

                <!-- Backend Technologies -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="text-indigo-600 mb-6">
                        <i class="fas fa-server text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Backend</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-node text-green-600"></i>
                            <span>Node.js</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-php text-purple-600"></i>
                            <span>PHP/Laravel</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-python text-blue-600"></i>
                            <span>Python/Django</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-gem text-red-600"></i>
                            <span>Ruby on Rails</span>
                        </div>
                    </div>
                </div>

                <!-- Mobile Technologies -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="text-green-600 mb-6">
                        <i class="fas fa-mobile-alt text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Mobile</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-android text-green-500"></i>
                            <span>Android/Kotlin</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-apple text-gray-800"></i>
                            <span>iOS/Swift</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-react text-blue-400"></i>
                            <span>React Native</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-mobile text-blue-500"></i>
                            <span>Flutter</span>
                        </div>
                    </div>
                </div>

                <!-- Cloud & DevOps -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="text-orange-600 mb-6">
                        <i class="fas fa-cloud text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Cloud & DevOps</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-aws text-orange-500"></i>
                            <span>AWS</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-docker text-blue-500"></i>
                            <span>Docker</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <!-- Custom Kubernetes Icon -->
                            <svg class="w-5 h-5 text-blue-600" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15.9998 0L29.3278 7.7599V23.2796L15.9998 31.0395L2.67188 23.2796V7.7599L15.9998 0Z" fill="currentColor" fill-opacity="0.2"/>
                                <path d="M15.9998 2L27.3278 8.5599V21.6796L15.9998 28.2395L4.67188 21.6796V8.5599L15.9998 2Z" stroke="currentColor" stroke-width="1.5"/>
                                <path d="M16 7L22 10.5V17.5L16 21L10 17.5V10.5L16 7Z" fill="currentColor"/>
                            </svg>
                            <span>Kubernetes</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <!-- Jenkins Icon -->
                            <svg class="w-5 h-5 text-red-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L20 6V18L12 22L4 18V6L12 2Z" fill="currentColor" fill-opacity="0.2"/>
                                <path d="M12 6L16 8V16L12 18L8 16V8L12 6Z" fill="currentColor"/>
                            </svg>
                            <span>CI/CD</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-network-wired text-purple-500"></i>
                            <span>Infrastructure as Code</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-shield-alt text-green-500"></i>
                            <span>DevSecOps</span>
                        </div>
                    </div>
                </div>

                <!-- Add Blockchain Technologies -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="text-blue-600 mb-6">
                        <i class="fas fa-cube text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Blockchain</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-ethereum text-purple-600"></i>
                            <span>Ethereum/Solidity</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-link text-blue-600"></i>
                            <span>Hyperledger</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-coins text-yellow-600"></i>
                            <span>Smart Contracts</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-wallet text-green-600"></i>
                            <span>DeFi Development</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-cubes text-red-600"></i>
                            <span>NFT Platforms</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-network-wired text-indigo-600"></i>
                            <span>Web3 Integration</span>
                        </div>
                    </div>
                </div>

                <!-- Add AI/ML Technologies -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="text-purple-600 mb-6">
                        <i class="fas fa-brain text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">AI & Machine Learning</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-robot text-blue-600"></i>
                            <span>Deep Learning</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-chart-line text-green-600"></i>
                            <span>Predictive Analytics</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-language text-indigo-600"></i>
                            <span>Natural Language Processing</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-eye text-orange-600"></i>
                            <span>Computer Vision</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-microchip text-red-600"></i>
                            <span>Neural Networks</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-cogs text-purple-600"></i>
                            <span>MLOps</span>
                        </div>
                    </div>
                </div>

                <!-- Add IoT Technologies -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="text-green-600 mb-6">
                        <i class="fas fa-network-wired text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Internet of Things</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-microchip text-blue-600"></i>
                            <span>Embedded Systems</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-wifi text-green-600"></i>
                            <span>IoT Connectivity</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-home text-yellow-600"></i>
                            <span>Smart Home Solutions</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-industry text-red-600"></i>
                            <span>Industrial IoT</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-chart-bar text-purple-600"></i>
                            <span>IoT Analytics</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-shield-alt text-indigo-600"></i>
                            <span>IoT Security</span>
                        </div>
                    </div>
                </div>

                <!-- Add Cybersecurity Technologies -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="text-red-600 mb-6">
                        <i class="fas fa-shield-alt text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Cybersecurity</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-lock text-blue-600"></i>
                            <span>Network Security</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-bug text-red-600"></i>
                            <span>Penetration Testing</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-user-shield text-green-600"></i>
                            <span>Identity Management</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-cloud-upload-alt text-purple-600"></i>
                            <span>Cloud Security</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-file-shield text-yellow-600"></i>
                            <span>Data Protection</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-virus-slash text-indigo-600"></i>
                            <span>Threat Detection</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Technologies -->
            <div class="mt-12 bg-white rounded-xl shadow-lg p-8">
                <h3 class="text-xl font-bold mb-6 text-center">Other Technologies We Work With</h3>
                <div class="flex flex-wrap justify-center gap-4">
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fas fa-database text-blue-600"></i>
                        <span>MongoDB</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fab fa-bootstrap text-purple-600"></i>
                        <span>Bootstrap</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fab fa-java text-red-600"></i>
                        <span>Java</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fas fa-wind text-teal-600"></i>
                        <span>Tailwind CSS</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fas fa-database text-orange-600"></i>
                        <span>MySQL</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fas fa-database text-red-600"></i>
                        <span>Redis</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fab fa-git-alt text-orange-600"></i>
                        <span>Git</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fab fa-sass text-pink-600"></i>
                        <span>Sass</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fab fa-wordpress text-blue-600"></i>
                        <span>WordPress</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fab fa-jenkins text-red-600"></i>
                        <span>Jenkins</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fab fa-npm text-red-600"></i>
                        <span>NPM</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fab fa-yarn text-blue-600"></i>
                        <span>Yarn</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fab fa-html5 text-orange-600"></i>
                        <span>HTML5</span>
                    </div>
                    <div class="px-4 py-2 bg-gray-100 rounded-full flex items-center space-x-2">
                        <i class="fab fa-css3-alt text-blue-600"></i>
                        <span>CSS3</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="py-16 bg-gray-50" id="services">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">Our Services</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">We provide comprehensive digital solutions to help your business grow and succeed in the modern marketplace</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Web Development -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                    <div class="text-blue-600 mb-4">
                        <i class="fas fa-code text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Web Development</h3>
                    <p class="text-gray-600 mb-4">Custom websites and web applications built with the latest technologies</p>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            Responsive Design
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            E-commerce Solutions
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            CMS Integration
                        </li>
                    </ul>
                </div>

                <!-- Mobile App Development -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                    <div class="text-blue-600 mb-4">
                        <i class="fas fa-mobile-alt text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Mobile App Development</h3>
                    <p class="text-gray-600 mb-4">Native and cross-platform mobile applications for iOS and Android</p>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            iOS & Android Apps
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            React Native
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            Flutter Development
                        </li>
                    </ul>
                </div>

                <!-- Digital Marketing -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                    <div class="text-blue-600 mb-4">
                        <i class="fas fa-chart-line text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Digital Marketing</h3>
                    <p class="text-gray-600 mb-4">Comprehensive digital marketing solutions to grow your business</p>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            SEO Optimization
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            Social Media Marketing
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            Content Marketing
                        </li>
                    </ul>
                </div>

                <!-- UI/UX Design -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                    <div class="text-blue-600 mb-4">
                        <i class="fas fa-pencil-ruler text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">UI/UX Design</h3>
                    <p class="text-gray-600 mb-4">User-centered design solutions for digital products</p>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            User Research
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            Wireframing
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            Prototyping
                        </li>
                    </ul>
                </div>

                <!-- Cloud Solutions -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                    <div class="text-blue-600 mb-4">
                        <i class="fas fa-cloud text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Cloud Solutions</h3>
                    <p class="text-gray-600 mb-4">Scalable cloud infrastructure and services</p>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            AWS Services
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            Cloud Migration
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            DevOps
                        </li>
                    </ul>
                </div>

                <!-- IT Consulting -->
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                    <div class="text-blue-600 mb-4">
                        <i class="fas fa-users-cog text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">IT Consulting</h3>
                    <p class="text-gray-600 mb-4">Strategic technology consulting and planning</p>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            Technology Assessment
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            Digital Strategy
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                            </svg>
                            Process Optimization
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
<!-- Journey Section -->

<!-- How We Empowering Toward Success Section -->
<section class="py-16 bg-gray-50" x-data="{
    steps: [
        {
            id: 'idea',
            icon: 'fa-lightbulb',
            color: 'text-yellow-500',
            title: 'Idea Generation',
            description: 'We generate innovative ideas based on your requirements and market trends to lay the foundation for your project.'
        },
        {
            id: 'research',
            icon: 'fa-magnifying-glass-chart',
            color: 'text-blue-500',
            title: 'Research',
            description: 'Comprehensive market analysis and competitor research to ensure your solution stands out in the market.'
        },
        {
            id: 'uiux',
            icon: 'fa-palette',
            color: 'text-purple-500',
            title: 'UI/UX',
            description: 'Creating intuitive and engaging user interfaces that deliver exceptional user experiences and drive engagement.'
        },
        {
            id: 'development',
            icon: 'fa-code',
            color: 'text-green-500',
            title: 'Development',
            description: 'Expert development using cutting-edge technologies to build robust and scalable solutions.'
        },
        {
            id: 'hosting',
            icon: 'fa-server',
            color: 'text-orange-500',
            title: 'Domain & Hosting',
            description: 'Secure and reliable hosting solutions with domain management to ensure your online presence is always accessible.'
        },
        {
            id: 'seo',
            icon: 'fa-chart-line',
            color: 'text-red-500',
            title: 'SEO',
            description: 'Strategic SEO implementation to improve your visibility and drive organic traffic to your digital platforms.'
        },
        {
            id: 'success',
            icon: 'fa-trophy',
            color: 'text-yellow-600',
            title: 'Success',
            description: 'Continuous monitoring and optimization to ensure your digital success and growth in the competitive market.'
        }
    ]
}">
    <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-6">How We Empowering Toward Success</h2>
        <p class="text-gray-600 max-w-3xl mx-auto text-center mb-12">
            A transparent journey to digital excellence. Discover our systematic approach, from envisioning your goals to delivering tailored solutions, as we collaborate to turn your vision into reality.
        </p>

        <div class="flex flex-wrap justify-center gap-4">
            <template x-for="step in steps" :key="step.id">
                <div
                    class="group relative w-full sm:w-[45%] md:w-[30%] lg:w-[180px] bg-white rounded-lg shadow p-4 transition-all duration-300 transform hover:scale-105 overflow-hidden"
                >
                    <div class="flex items-center gap-2 mb-2">
                        <i :class="['fas', step.icon, step.color, 'text-2xl']"></i>
                        <h3 class="text-sm font-semibold" x-text="step.title"></h3>
                    </div>
                    <!-- Hidden by default, fades in on hover -->
                    <p
                        class="text-xs text-gray-600 opacity-0 max-h-0 group-hover:opacity-100 group-hover:max-h-[300px] group-hover:mt-2 transition-all duration-300 ease-in-out"
                        x-text="step.description"
                    ></p>
                </div>
            </template>
        </div>
    </div>
</section>





<!-- Our Effective Work Process -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-6">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">Our Effective Work Process</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">Our methodology for building strong client relationships. Discover how we foster open communication, prioritize your goals, and deliver exceptional results through our collaborative approach to working together.</p>
        </div>
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="bg-white p-8 rounded-lg shadow-md text-center">
                <div class="text-blue-600 text-4xl mb-4">
                    <i class="fas fa-comments"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">Discuss</h3>
                <p class="text-gray-600">We initiate the process by engaging in open and thorough discussions with you. We listen attentively to understand your specific requirements, goals, and expectations.</p>
            </div>
            <div class="bg-white p-8 rounded-lg shadow-md text-center">
                <div class="text-green-600 text-4xl mb-4">
                    <i class="fas fa-handshake"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">Deal</h3>
                <p class="text-gray-600">After gaining a deep understanding of your needs, We work closely with you, crafting tailored proposals and transparent deal that align with your budget and objectives.</p>
            </div>
            <div class="bg-white p-8 rounded-lg shadow-md text-center">
                <div class="text-orange-600 text-4xl mb-4">
                    <i class="fas fa-code"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">Develop</h3>
                <p class="text-gray-600">Once the deal is finalized, our expert team leverages technical prowess and industry insights to develop innovative solutions that precisely cater to your requirements, keeping you involved throughout the process.</p>
            </div>
            <div class="bg-white p-8 rounded-lg shadow-md text-center">
                <div class="text-purple-600 text-4xl mb-4">
                    <i class="fas fa-truck"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">Delivery</h3>
                <p class="text-gray-600">With meticulous attention to detail, we ensure timely and successful project delivery, exceeding expectations and prioritizing your satisfaction.</p>
            </div>
        </div>
    </div>
</section>

    <!-- Trusted by Industry Leaders -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">Trusted by Industry Leaders</h2>
                <p class="text-gray-600">Partnering with global brands to deliver excellence</p>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 flex items-center justify-center">
                    <img src="https://placehold.co/200x80/4F46E5/FFFFFF/png?text=Microsoft" alt="Microsoft" class="max-h-12">
                </div>
                <div class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 flex items-center justify-center">
                    <img src="https://placehold.co/200x80/3B82F6/FFFFFF/png?text=Google" alt="Google" class="max-h-12">
                </div>
                <div class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 flex items-center justify-center">
                    <img src="https://placehold.co/200x80/6366F1/FFFFFF/png?text=Amazon" alt="Amazon" class="max-h-12">
                </div>
                <div class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 flex items-center justify-center">
                    <img src="https://placehold.co/200x80/8B5CF6/FFFFFF/png?text=IBM" alt="IBM" class="max-h-12">
                </div>
            </div>
        </div>
    </section>

    <!-- Client Reviews Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">What Our Clients Say</h2>
                <p class="text-gray-600">Real feedback from our valued customers</p>
            </div>

            <!-- Testimonials Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Testimonial 1 -->
                <div class="bg-gray-50 rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400 flex">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <span class="ml-2 text-gray-600">5.0</span>
                    </div>
                    <p class="text-gray-600 mb-6">"Pitor transformed our business with their innovative solutions. Their team's expertise and dedication were exceptional."</p>
                    <div class="flex items-center">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="John Smith" class="w-12 h-12 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-semibold">John Smith</h4>
                            <p class="text-gray-600 text-sm">CEO, Tech Corp</p>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 2 -->
                <div class="bg-gray-50 rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400 flex">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <span class="ml-2 text-gray-600">5.0</span>
                    </div>
                    <p class="text-gray-600 mb-6">"The team at Pitor delivered our project on time and exceeded our expectations. Highly recommended!"</p>
                    <div class="flex items-center">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Sarah Johnson" class="w-12 h-12 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-semibold">Sarah Johnson</h4>
                            <p class="text-gray-600 text-sm">CTO, Innovation Labs</p>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 3 -->
                <div class="bg-gray-50 rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400 flex">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <span class="ml-2 text-gray-600">5.0</span>
                    </div>
                    <p class="text-gray-600 mb-6">"Professional, responsive, and incredibly skilled. Pitor helped us achieve our digital transformation goals."</p>
                    <div class="flex items-center">
                        <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="Michael Brown" class="w-12 h-12 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-semibold">Michael Brown</h4>
                            <p class="text-gray-600 text-sm">Director, Digital Solutions</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>





    <!-- Mobile Menu Dropdown (Hidden by default) -->
    <div class="md:hidden absolute top-full left-0 right-0 bg-white shadow-lg py-2 hidden">
        <a href="/" class="block px-6 py-2 text-gray-700 hover:bg-blue-50">Home</a>
        <a href="/products.html" class="block px-6 py-2 text-gray-700 hover:bg-blue-50">Products</a>
        <a href="/services.html" class="block px-6 py-2 text-gray-700 hover:bg-blue-50">Services</a>
        <a href="/about.html" class="block px-6 py-2 text-gray-700 hover:bg-blue-50">About</a>
        <a href="/contact.html" class="block px-6 py-2 text-gray-700 hover:bg-blue-50">Contact</a>
    </div>

    <!-- Add this before closing body tag -->
    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.querySelector('.md\\:hidden button');
        const mobileMenu = document.querySelector('.md\\:hidden.absolute');

        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!mobileMenuButton.contains(e.target) && !mobileMenu.contains(e.target)) {
                mobileMenu.classList.add('hidden');
            }
        });
    </script>

    <!-- Contact Buttons -->
    <div class="fixed bottom-8 left-8 z-50 flex flex-col space-y-4">
        <!-- Toggle Button -->
        <div class="relative group">
            <button id="toggleChat"
                    class="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 text-white rounded-full shadow-lg hover:from-green-600 hover:to-teal-600 transition-all duration-300">
                <i class="fas fa-comments text-2xl transition-transform duration-300" id="toggleIcon"></i>
            </button>
            <span class="absolute left-full ml-4 top-1/2 transform -translate-y-1/2 whitespace-nowrap bg-black bg-opacity-75 text-white text-sm py-2 px-4 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                Chat with us
            </span>
        </div>

        <!-- Social Chat Links -->
        <div id="chatButtons" class="flex flex-col-reverse space-y-reverse space-y-4 scale-0 opacity-0 transition-all duration-300 origin-top absolute bottom-20">
            <!-- WhatsApp -->
            <a href="https://wa.me/+8801818898189"
               target="_blank"
               class="flex items-center justify-center w-14 h-14 bg-green-500 text-white rounded-full shadow-lg hover:bg-green-600 transition-colors">
                <i class="fab fa-whatsapp text-2xl"></i>
            </a>

            <!-- Telegram -->
            <a href="https://t.me/pitor"
               target="_blank"
               class="flex items-center justify-center w-14 h-14 bg-blue-400 text-white rounded-full shadow-lg hover:bg-blue-500 transition-colors">
                <i class="fab fa-telegram-plane text-2xl"></i>
            </a>

            <!-- Messenger -->
            <a href="https://m.me/pitor.net"
               target="_blank"
               class="flex items-center justify-center w-14 h-14 bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-600 transition-colors">
                <i class="fab fa-facebook-messenger text-2xl"></i>
            </a>

            <!-- Email -->
            <a href="#contact"
               class="flex items-center justify-center w-14 h-14 bg-red-500 text-white rounded-full shadow-lg hover:bg-red-600 transition-colors">
                <i class="fas fa-envelope text-2xl"></i>
            </a>



        </div>
    </div>
    <!-- Add this to your existing script section -->
    <script>
        // Chat buttons toggle functionality
        const toggleChat = document.getElementById('toggleChat');
        const chatButtons = document.getElementById('chatButtons');
        const toggleIcon = document.getElementById('toggleIcon');
        let isOpen = false;

        toggleChat.addEventListener('click', () => {
            isOpen = !isOpen;

            if (isOpen) {
                // Open state
                chatButtons.classList.remove('scale-0', 'opacity-0');
                chatButtons.classList.add('scale-100', 'opacity-100');
                toggleIcon.classList.add('rotate-45');
                toggleIcon.classList.remove('fa-comments');
                toggleIcon.classList.add('fa-times');
            } else {
                // Closed state
                chatButtons.classList.remove('scale-100', 'opacity-100');
                chatButtons.classList.add('scale-0', 'opacity-0');
                toggleIcon.classList.remove('rotate-45');
                toggleIcon.classList.remove('fa-times');
                toggleIcon.classList.add('fa-comments');
            }
        });
    </script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // For mobile dropdowns without Alpine.js
        document.querySelectorAll('#mobile-menu [x-data]').forEach(function(dropdown) {
            const button = dropdown.querySelector('button');
            const content = dropdown.querySelector('div[x-show]');

            // Initialize - make sure dropdowns are hidden
            content.style.display = 'none';

            button.addEventListener('click', function() {
                if (content.style.display === 'none') {
                    content.style.display = 'block';
                } else {
                    content.style.display = 'none';
                }
            });
        });
    });
</script>

    <!-- Back to Top Button -->
    <button id="backToTop" class="fixed bottom-8 right-8 z-50 bg-blue-600 text-white rounded-full p-3 shadow-lg opacity-0 invisible transition-all duration-300 hover:bg-blue-700">
        <i class="fas fa-arrow-up text-lg"></i>
    </button>

    <!-- Simple Footer Divider -->
    <div class="border-t border-gray-200"></div>

    <!-- Footer -->
    <footer class="relative text-white overflow-hidden">
        <!-- Modern Gradient Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-slate-900 via-indigo-900 to-slate-800"></div>
        
        <!-- Animated Geometric Patterns -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <!-- Floating Shapes -->
            <div class="absolute -top-24 -right-24 w-80 h-80 bg-gradient-to-br from-blue-500/10 to-indigo-600/5 rounded-full animate-pulse"></div>
            <div class="absolute top-1/3 -left-32 w-64 h-64 bg-gradient-to-tr from-purple-500/8 to-blue-500/10 rounded-full animate-pulse animation-delay-700"></div>
            <div class="absolute -bottom-32 right-1/4 w-56 h-56 bg-gradient-to-tl from-indigo-500/10 to-purple-600/8 rounded-full animate-pulse animation-delay-1000"></div>
            
            <!-- Subtle Grid Pattern -->
            <div class="absolute inset-0 opacity-5">
                <div class="w-full h-full" style="background-image: radial-gradient(circle at 2px 2px, white 1px, transparent 0); background-size: 40px 40px;"></div>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="container mx-auto px-6 py-16 relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- About Column -->
                <div class="lg:col-span-2">
                    <div class="mb-8">
                        <img src="images/Logo.png" alt="Pitor Logo" class="w-40 h-auto mb-6 filter brightness-110">
                        <p class="text-gray-300 text-lg leading-relaxed mb-6">
                            Leading software development company providing innovative digital solutions for businesses worldwide. We transform ideas into powerful digital experiences.
                        </p>
                        
                        <!-- Enhanced Stats -->
                        <div class="grid grid-cols-3 gap-4 mb-8">
                            <div class="text-center p-4 bg-white/5 rounded-lg backdrop-blur-sm border border-white/10">
                                <div class="text-2xl font-bold text-blue-400">500+</div>
                                <div class="text-sm text-gray-400">Projects</div>
                            </div>
                            <div class="text-center p-4 bg-white/5 rounded-lg backdrop-blur-sm border border-white/10">
                                <div class="text-2xl font-bold text-green-400">200+</div>
                                <div class="text-sm text-gray-400">Clients</div>
                            </div>
                            <div class="text-center p-4 bg-white/5 rounded-lg backdrop-blur-sm border border-white/10">
                                <div class="text-2xl font-bold text-purple-400">5+</div>
                                <div class="text-sm text-gray-400">Years</div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Social Media -->
                    <div>
                        <h4 class="text-lg font-semibold mb-4 text-white">Connect With Us</h4>
                        <div class="flex space-x-4">
                            <a href="https://www.facebook.com/pitor.net/" target="_blank" 
                               class="group bg-white/10 hover:bg-blue-600 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-facebook-f text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-blue-400 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-twitter text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-blue-700 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-linkedin-in text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-gradient-to-tr hover:from-purple-600 hover:to-pink-500 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-instagram text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-red-600 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-youtube text-lg group-hover:text-white transition-colors"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div>
                    <h3 class="text-xl font-bold mb-6 relative text-white">
                        <span class="relative z-10">Quick Links</span>
                        <span class="absolute bottom-0 left-0 w-16 h-0.5 bg-gradient-to-r from-blue-400 to-transparent"></span>
                    </h3>
                    <ul class="space-y-3">
                        <li><a href="privacy-policy.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Privacy Policy
                        </a></li>
                        <li><a href="refund-policy.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Refund Policy
                        </a></li>
                        <li><a href="terms-of-services.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Terms of Service
                        </a></li>
                        <li><a href="about.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            About Us
                        </a></li>
                        <li><a href="contact.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Contact Us
                        </a></li>
                        <li><a href="careers.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Careers
                        </a></li>
                    </ul>
                </div>

                <!-- Services & Contact Column -->
                <div>
                    <h3 class="text-xl font-bold mb-6 relative text-white">
                        <span class="relative z-10">Our Services</span>
                        <span class="absolute bottom-0 left-0 w-16 h-0.5 bg-gradient-to-r from-blue-400 to-transparent"></span>
                    </h3>
                    <ul class="space-y-3 mb-8">
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Web Development
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Mobile Apps
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Cloud Solutions
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            UI/UX Design
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            IT Consulting
                        </a></li>
                    </ul>

                    <!-- Contact Info -->
                    <div class="bg-white/5 rounded-xl p-6 backdrop-blur-sm border border-white/10">
                        <h4 class="font-semibold mb-4 text-white flex items-center">
                            <i class="fas fa-headset mr-2 text-blue-400"></i>
                            24/7 Support
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-envelope text-blue-400 w-4"></i>
                                <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-white transition-colors"><EMAIL></a>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-phone-alt text-blue-400 w-4"></i>
                                <a href="tel:+8801818898189" class="text-gray-300 hover:text-white transition-colors">+880 1818 898 189</a>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-clock text-blue-400 w-4"></i>
                                <span class="text-gray-300">Mon - Fri: 9:00 AM - 6:00 PM</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Enhanced Copyright Section -->
            <div class="border-t border-white/10 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="text-center md:text-left">
                        <p class="text-gray-300">
                            &copy; 2025 <span class="text-white font-bold bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">Pitor</span>. All rights reserved.
                        </p>
                        <p class="text-sm text-gray-400 mt-1">Empowering businesses through innovative digital solutions</p>
                    </div>
                    <div class="flex items-center space-x-6 text-sm text-gray-400">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-shield-alt text-green-400"></i>
                            <span>SSL Secured</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-award text-yellow-400"></i>
                            <span>ISO Certified</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-leaf text-green-400"></i>
                            <span>Eco Friendly</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button Script -->
    <script>
        // Back to top button functionality
        const backToTopButton = document.getElementById('backToTop');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.remove('opacity-0', 'invisible');
                backToTopButton.classList.add('opacity-100', 'visible');
            } else {
                backToTopButton.classList.remove('opacity-100', 'visible');
                backToTopButton.classList.add('opacity-0', 'invisible');
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>