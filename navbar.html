<!-- Header -->
<header class="bg-white shadow-md fixed w-full z-50">
    <nav class="container mx-auto px-6 py-4">
        <div class="flex justify-between items-center">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="index.html" class="flex items-center space-x-3">
                    <!-- Logo Mark -->
                    <div class="relative w-10 h-10">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg transform rotate-3 transition-transform group-hover:rotate-6"></div>
                        <div class="absolute inset-0 bg-white rounded-lg transform -rotate-3 transition-transform group-hover:-rotate-6">
                            <div class="absolute inset-2 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-md flex items-center justify-center">
                                <span class="text-white text-xl font-bold">P</span>
                            </div>
                        </div>
                    </div>
                    <!-- Logo Type -->
                    <div class="flex flex-col">
                        <span class="text-xl font-bold text-gray-900">Pitor</span>
                        <span class="text-sm text-gray-600">Digital Solutions</span>
                    </div>
                </a>
            </div>
            
            <!-- Desktop Menu -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-gray-700 hover:text-blue-600">Home</a>
                <div class="relative group">
                    <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                        <span>Solutions</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                        <div class="p-4 space-y-3">
                            <a href="e-commerce-solution.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-shopping-cart text-blue-600"></i>
                                <div>
                                    <div class="font-semibold">E-Commerce Solution</div>
                                    <div class="text-sm text-gray-600">Complete online store management</div>
                                </div>
                            </a>
                            <a href="crm-system.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-users text-green-600"></i>
                                <div>
                                    <div class="font-semibold">CRM System</div>
                                    <div class="text-sm text-gray-600">Customer relationship management</div>
                                </div>
                            </a>
                            <a href="pos-system.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-cash-register text-orange-600"></i>
                                <div>
                                    <div class="font-semibold">POS System</div>
                                    <div class="text-sm text-gray-600">Point of sale management</div>
                                </div>
                            </a>
                            <a href="payment-gateway.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-credit-card text-purple-600"></i>
                                <div>
                                    <div class="font-semibold">Payment Gateway</div>
                                    <div class="text-sm text-gray-600">Secure payment processing</div>
                                </div>
                            </a>
                            <a href="flight-booking.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-plane text-purple-600"></i>
                                <div>
                                    <div class="font-semibold">Flight Booking</div>
                                    <div class="text-sm text-gray-600">Secure Flight Ticket processing</div>
                                </div>
                            </a>
                            <div class="border-t border-gray-100 my-2"></div>
                            <a href="solutions.html" class="flex items-center justify-between text-sm text-blue-600 hover:text-blue-700 p-2">
                                <span>View All Solutions</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <a href="products.html" class="text-gray-700 hover:text-blue-600">Products</a>
                <a href="#services" class="text-gray-700 hover:text-blue-600">Services</a>
                <a href="about.html" class="text-gray-700 hover:text-blue-600">About</a>
                <a href="login.html" class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Login</span>
                </a>
            </div>

            <!-- Mobile Menu Button -->
            <div class="md:hidden flex items-center space-x-4">
                <a href="login.html" class="text-gray-700 hover:text-blue-600">
                    <i class="fas fa-sign-in-alt text-xl"></i>
                </a>
                <button id="mobile-menu-button" class="text-gray-700 hover:text-blue-600 focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu (Hidden by Default) -->
    <div id="mobile-menu" class="md:hidden hidden bg-white shadow-md">
        <div class="container mx-auto px-6 py-4">
            <a href="index.html" class="block text-gray-700 hover:text-blue-600 py-2">Home</a>
            <!-- Add Solutions Dropdown for Mobile -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                    <span>Solutions</span>
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div x-show="open" class="pl-4 space-y-2">
                    <a href="e-commerce-solution.html" class="block text-gray-700 hover:text-blue-600 py-2">E-Commerce Solution</a>
                    <a href="crm-system.html" class="block text-gray-700 hover:text-blue-600 py-2">CRM System</a>
                    <a href="pos-system.html" class="block text-gray-700 hover:text-blue-600 py-2">POS System</a>
                    <a href="payment-gateway.html" class="block text-gray-700 hover:text-blue-600 py-2">Payment Gateway</a>
                    <a href="flight-booking.html" class="block text-gray-700 hover:text-blue-600 py-2">Flight Booking</a>
                    <a href="solutions.html" class="block text-blue-600 hover:text-blue-700 py-2">View All Solutions</a>
                </div>
            </div>
            <a href="products.html" class="block text-gray-700 hover:text-blue-600 py-2">Products</a>
            <a href="services.html" class="block text-gray-700 hover:text-blue-600 py-2">Services</a>
            <a href="about.html" class="block text-gray-700 hover:text-blue-600 py-2">About</a>
            <a href="login.html" class="block text-gray-700 hover:text-blue-600 py-2">Login</a>
        </div>
    </div>
</header>