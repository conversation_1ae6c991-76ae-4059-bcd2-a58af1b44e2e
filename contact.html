<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pitor - Digital Solutions Provider</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="icon" type="image/png" href="images/favicon.png">
    <!-- Social Media Sharing -->
    <meta property="og:image" content="images/social-share-image.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta name="twitter:image" content="images/social-share-image.png">
    <link rel="image_src" href="images/social-share-image.png">
</head>
<body>

    <!-- Header -->
    <header class="bg-white shadow-md fixed w-full z-50">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-3">
                        <!-- Logo Mark -->
                        <img src="images/Logo.png" alt="Pitor Logo" class="w-18 h-10">
                        <!-- Logo Type -->
                        
                    </a>
                </div>
                
                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-blue-600">Home</a>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Services</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-laptop-code text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Web Development</div>
                                        <div class="text-sm text-gray-600">Custom website solutions</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-mobile-alt text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">Mobile Development</div>
                                        <div class="text-sm text-gray-600">iOS and Android applications</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-paint-brush text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">UI/UX Design</div>
                                        <div class="text-sm text-gray-600">User interface and experience</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-bullhorn text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Digital Marketing</div>
                                        <div class="text-sm text-gray-600">SEO and online advertising</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-comments text-red-600"></i>
                                    <div>
                                        <div class="font-semibold">IT Consulting</div>
                                        <div class="text-sm text-gray-600">Expert technology advice</div>
                                    </div>
                                </a>
                                <div class="border-t border-gray-100 my-2"></div>
                                <a href="#services" class="flex items-center justify-between text-sm text-blue-600 hover:text-blue-700 p-2">
                                    <span>View All Services</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Solutions</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="e-commerce-solution.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-shopping-cart text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">E-Commerce Solution</div>
                                        <div class="text-sm text-gray-600">Complete online store management</div>
                                    </div>
                                </a>
                                <a href="crm-system.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-users text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">CRM System</div>
                                        <div class="text-sm text-gray-600">Customer relationship management</div>
                                    </div>
                                </a>
                                <a href="pos-system.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-cash-register text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">POS System</div>
                                        <div class="text-sm text-gray-600">Point of sale management</div>
                                    </div>
                                </a>
                                <a href="payment-gateway.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-credit-card text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Payment Gateway</div>
                                        <div class="text-sm text-gray-600">Secure payment processing</div>
                                    </div>
                                </a>

                                <a href="flight-booking.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-credit-card text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Flight Booking</div>
                                        <div class="text-sm text-gray-600">Secure Flight Ticket processing</div>
                                    </div>
                                </a>


                                <div class="border-t border-gray-100 my-2"></div>
                                <a href="solutions.html" class="flex items-center justify-between text-sm text-blue-600 hover:text-blue-700 p-2">
                                    <span>View All Solutions</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>

                               


                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Products</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="products.html?category=Management%20Systems" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-tasks text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Management Systems</div>
                                        <div class="text-sm text-gray-600">Business process automation</div>
                                    </div>
                                </a>
                                <a href="products.html?category=Financial%20Solutions" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-chart-line text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">Financial Solutions</div>
                                        <div class="text-sm text-gray-600">Payment and accounting tools</div>
                                    </div>
                                </a>
                                <a href="products.html?category=Booking%20Solutions" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-calendar-check text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">Booking Solutions</div>
                                        <div class="text-sm text-gray-600">Reservation and scheduling</div>
                                    </div>
                                </a>
                                <a href="products.html?category=Mobile%20Applications" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-mobile-alt text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Mobile Applications</div>
                                        <div class="text-sm text-gray-600">iOS and Android solutions</div>
                                    </div>
                                </a>
                                <a href="products.html?category=E-Commerce" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-shopping-cart text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">E-Commerce Solutions</div>
                                        <div class="text-sm text-gray-600">Online store management</div>
                                    </div>
                                </a>

                                <div class="border-t border-gray-100 my-2"></div>
                                <a href="products.html" class="flex items-center justify-between text-sm text-blue-600 hover:text-blue-700 p-2">
                                    <span>View All Products</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>

                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Hosting</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-server text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Shared Hosting</div>
                                        <div class="text-sm text-gray-600">Affordable hosting solutions</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-server text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">VPS Hosting</div>
                                        <div class="text-sm text-gray-600">Virtual private server hosting</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-hdd text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">Dedicated Servers</div>
                                        <div class="text-sm text-gray-600">Full server control and resources</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-cloud text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Cloud Hosting</div>
                                        <div class="text-sm text-gray-600">Scalable cloud infrastructure</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-globe text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Domain Name Registration</div>
                                        <div class="text-sm text-gray-600">Secure your online identity</div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Company</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="about.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-info-circle text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">About</div>
                                        <div class="text-sm text-gray-600">Learn about our company</div>
                                    </div>
                                </a>
                                <a href="contact.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-envelope text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">Contact Us</div>
                                        <div class="text-sm text-gray-600">Get in touch with us</div>
                                    </div>
                                </a>
                                <a href="careers.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-briefcase text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">Careers</div>
                                        <div class="text-sm text-gray-600">Join our team</div>
                                    </div>
                                </a>
                                <a href="#team" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-users text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Our Team</div>
                                        <div class="text-sm text-gray-600">Meet our experts</div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                    <a href="login.html" class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Login</span>
                    </a>
                </div>
    
                <!-- Mobile Menu Button -->
                <div class="md:hidden flex items-center space-x-4">
                    <a href="login.html" class="text-gray-700 hover:text-blue-600">
                        <i class="fas fa-sign-in-alt text-xl"></i>
                    </a>
                    <button id="mobile-menu-button" class="text-gray-700 hover:text-blue-600 focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </nav>
    
        <!-- Mobile Menu (Hidden by Default) -->
        <div id="mobile-menu" class="md:hidden hidden bg-white shadow-md">
            <div class="container mx-auto px-6 py-4">
                <a href="index.html" class="block text-gray-700 hover:text-blue-600 py-2">Home</a>
                <!-- Services Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Services</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">Web Development</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">Mobile Development</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">UI/UX Design</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">Digital Marketing</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">IT Consulting</a>
                        <a href="#services" class="block text-blue-600 hover:text-blue-700 py-2">View All Services</a>
                    </div>
                </div>
                <!-- Add Solutions Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Solutions</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="e-commerce-solution.html" class="block text-gray-700 hover:text-blue-600 py-2">E-Commerce Solution</a>
                        <a href="crm-system.html" class="block text-gray-700 hover:text-blue-600 py-2">CRM System</a>
                        <a href="pos-system.html" class="block text-gray-700 hover:text-blue-600 py-2">POS System</a>
                        <a href="payment-gateway.html" class="block text-gray-700 hover:text-blue-600 py-2">Payment Gateway</a>
                        <a href="solutions.html" class="block text-blue-600 hover:text-blue-700 py-2">View All Solutions</a>
                    </div>
                </div>
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Products</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="products.html?category=Management%20Systems" class="block text-gray-700 hover:text-blue-600 py-2">Management Systems</a>
                        <a href="products.html?category=Financial%20Solutions" class="block text-gray-700 hover:text-blue-600 py-2">Financial Solutions</a>
                        <a href="products.html?category=Booking%20Solutions" class="block text-gray-700 hover:text-blue-600 py-2">Booking Solutions</a>
                        <a href="products.html?category=Mobile%20Applications" class="block text-gray-700 hover:text-blue-600 py-2">Mobile Applications</a>
                        <a href="products.html?category=E-Commerce" class="block text-gray-700 hover:text-blue-600 py-2">E-Commerce Solutions</a>
                        <a href="products.html" class="block text-blue-600 hover:text-blue-700 py-2">View All Products</a>
                    </div>
                </div>
                <!-- Add Hosting Services Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Hosting</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Shared Hosting</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">VPS Hosting</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Dedicated Servers</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Cloud Hosting</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Domain Name Registration</a>
                    </div>
                </div>
                <a href="services.html" class="block text-gray-700 hover:text-blue-600 py-2">Services</a>
                <!-- Add Company Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Company</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="about.html" class="block text-gray-700 hover:text-blue-600 py-2">About</a>
                        <a href="contact.html" class="block text-gray-700 hover:text-blue-600 py-2">Contact Us</a>
                        <a href="careers.html" class="block text-gray-700 hover:text-blue-600 py-2">Careers</a>
                        <a href="#team" class="block text-gray-700 hover:text-blue-600 py-2">Our Team</a>
                    </div>
                </div>
                <a href="login.html" class="block text-gray-700 hover:text-blue-600 py-2">Login</a>
            </div>
        </div>
    </header>

</body>



    <!-- Main Content -->
    <main class="container mx-auto px-6 py-24 flex flex-col md:flex-row gap-12">
        <!-- Contact Form -->
        <section class="w-full md:w-2/3 bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-2xl font-bold mb-6 text-blue-700 flex items-center gap-2"><i class="fas fa-envelope"></i> Contact Us</h2>
            <form class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                        <input type="text" id="name" name="name" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" placeholder="Your Name">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" id="email" name="email" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" placeholder="<EMAIL>">
                    </div>
                </div>
                <div>
                    <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
                    <input type="text" id="subject" name="subject" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" placeholder="Subject">
                </div>
                <div>
                    <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
                    <textarea id="message" name="message" rows="5" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" placeholder="Type your message..."></textarea>
                </div>
                <button type="submit" class="w-full md:w-auto px-8 py-3 bg-blue-600 text-white font-semibold rounded-md shadow hover:bg-blue-700 transition">Send Message</button>
            </form>
        </section>
        <!-- More Info -->
        <aside class="w-full md:w-1/3 flex flex-col gap-8">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h3 class="text-xl font-bold mb-4 text-blue-700 flex items-center gap-2"><i class="fas fa-info-circle"></i> Contact Information</h3>
                
                <!-- USA Office -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-blue-600 flex items-center gap-2">
                        <i class="fas fa-flag-usa text-blue-500"></i>
                        USA Office
                    </h4>
                    <div class="bg-blue-50 rounded-lg p-4 mb-3">
                        <div class="flex items-start gap-3">
                            <i class="fas fa-map-marker-alt text-blue-600 mt-1"></i>
                            <div class="text-gray-700">
                                <div class="font-medium">1079 E LOVEJOY ST</div>
                                <div>New York, NY 14206</div>
                                <div>United States</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bangladesh Office -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-green-600 flex items-center gap-2">
                        <i class="fas fa-home text-green-500"></i>
                        Bangladesh Office
                    </h4>
                    <div class="bg-green-50 rounded-lg p-4 mb-3">
                        <div class="flex items-start gap-3">
                            <i class="fas fa-map-marker-alt text-green-600 mt-1"></i>
                            <div class="text-gray-700">
                                <div class="font-medium">House No: 12, Road No: 13</div>
                                <div>Nikunja-2, Khilkhet</div>
                                <div>Dhaka-1229, Bangladesh</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Details -->
                <div class="border-t border-gray-200 pt-4">
                    <h4 class="text-lg font-semibold mb-3 text-gray-700">Get In Touch</h4>
                    <ul class="space-y-3">
                        <li class="flex items-center gap-3">
                            <i class="fas fa-phone-alt text-green-600"></i>
                            <span class="text-gray-700">+8801818898189</span>
                        </li>
                        <li class="flex items-center gap-3">
                            <i class="fas fa-envelope text-orange-600"></i>
                            <span class="text-gray-700"><EMAIL></span>
                        </li>
                        <li class="flex items-center gap-3">
                            <i class="fas fa-clock text-purple-600"></i>
                            <span class="text-gray-700">Mon - Fri: 9:00 AM - 6:00 PM</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h3 class="text-xl font-bold mb-4 text-blue-700 flex items-center gap-2"><i class="fas fa-share-alt"></i> Connect With Us</h3>
                <div class="flex gap-4">
                    <a href="#" class="text-blue-600 hover:text-blue-800 text-2xl"><i class="fab fa-facebook"></i></a>
                    <a href="#" class="text-blue-400 hover:text-blue-600 text-2xl"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-pink-600 hover:text-pink-800 text-2xl"><i class="fab fa-instagram"></i></a>
                    <a href="#" class="text-blue-700 hover:text-blue-900 text-2xl"><i class="fab fa-linkedin"></i></a>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h3 class="text-xl font-bold mb-4 text-blue-700 flex items-center gap-2"><i class="fas fa-map"></i> Our Location</h3>
                <iframe class="w-full h-48 rounded-md border-0" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3151.835434509374!2d144.9537363155042!3d-37.81627974202145!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad65d43f1f7fd81%3A0xf5777e6c7e6e7e7e!2s123%20Main%20St%2C%20Melbourne%20VIC%203000%2C%20Australia!5e0!3m2!1sen!2sus!4v1614037700000!5m2!1sen!2sus" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
            </div>
        </aside>
    </main>
    <!-- Footer -->
    <button id="backToTop" class="fixed bottom-8 right-8 z-50 bg-blue-600 text-white rounded-full p-3 shadow-lg opacity-0 invisible transition-all duration-300 hover:bg-blue-700">
        <i class="fas fa-arrow-up text-lg"></i>
    </button>

    <!-- Simple Footer Divider -->
    <div class="border-t border-gray-200"></div>

    <!-- Footer -->
    <footer class="relative text-white overflow-hidden">
        <!-- Modern Gradient Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-slate-900 via-indigo-900 to-slate-800"></div>
        
        <!-- Animated Geometric Patterns -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <!-- Floating Shapes -->
            <div class="absolute -top-24 -right-24 w-80 h-80 bg-gradient-to-br from-blue-500/10 to-indigo-600/5 rounded-full animate-pulse"></div>
            <div class="absolute top-1/3 -left-32 w-64 h-64 bg-gradient-to-tr from-purple-500/8 to-blue-500/10 rounded-full animate-pulse animation-delay-700"></div>
            <div class="absolute -bottom-32 right-1/4 w-56 h-56 bg-gradient-to-tl from-indigo-500/10 to-purple-600/8 rounded-full animate-pulse animation-delay-1000"></div>
            
            <!-- Subtle Grid Pattern -->
            <div class="absolute inset-0 opacity-5">
                <div class="w-full h-full" style="background-image: radial-gradient(circle at 2px 2px, white 1px, transparent 0); background-size: 40px 40px;"></div>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="container mx-auto px-6 py-16 relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- About Column -->
                <div class="lg:col-span-2">
                    <div class="mb-8">
                        <img src="images/Logo.png" alt="Pitor Logo" class="w-40 h-auto mb-6 filter brightness-110">
                        <p class="text-gray-300 text-lg leading-relaxed mb-6">
                            Leading software development company providing innovative digital solutions for businesses worldwide. We transform ideas into powerful digital experiences.
                        </p>
                        
                        <!-- Enhanced Stats -->
                        <div class="grid grid-cols-3 gap-4 mb-8">
                            <div class="text-center p-4 bg-white/5 rounded-lg backdrop-blur-sm border border-white/10">
                                <div class="text-2xl font-bold text-blue-400">500+</div>
                                <div class="text-sm text-gray-400">Projects</div>
                            </div>
                            <div class="text-center p-4 bg-white/5 rounded-lg backdrop-blur-sm border border-white/10">
                                <div class="text-2xl font-bold text-green-400">200+</div>
                                <div class="text-sm text-gray-400">Clients</div>
                            </div>
                            <div class="text-center p-4 bg-white/5 rounded-lg backdrop-blur-sm border border-white/10">
                                <div class="text-2xl font-bold text-purple-400">5+</div>
                                <div class="text-sm text-gray-400">Years</div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Social Media -->
                    <div>
                        <h4 class="text-lg font-semibold mb-4 text-white">Connect With Us</h4>
                        <div class="flex space-x-4">
                            <a href="https://www.facebook.com/pitor.net/" target="_blank" 
                               class="group bg-white/10 hover:bg-blue-600 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-facebook-f text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-blue-400 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-twitter text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-blue-700 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-linkedin-in text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-gradient-to-tr hover:from-purple-600 hover:to-pink-500 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-instagram text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-red-600 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-youtube text-lg group-hover:text-white transition-colors"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div>
                    <h3 class="text-xl font-bold mb-6 relative text-white">
                        <span class="relative z-10">Quick Links</span>
                        <span class="absolute bottom-0 left-0 w-16 h-0.5 bg-gradient-to-r from-blue-400 to-transparent"></span>
                    </h3>
                    <ul class="space-y-3">
                        <li><a href="privacy-policy.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Privacy Policy
                        </a></li>
                        <li><a href="refund-policy.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Refund Policy
                        </a></li>
                        <li><a href="terms-of-services.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Terms of Service
                        </a></li>
                        <li><a href="about.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            About Us
                        </a></li>
                        <li><a href="contact.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Contact Us
                        </a></li>
                        <li><a href="careers.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Careers
                        </a></li>
                    </ul>
                </div>

                <!-- Services & Contact Column -->
                <div>
                    <h3 class="text-xl font-bold mb-6 relative text-white">
                        <span class="relative z-10">Our Services</span>
                        <span class="absolute bottom-0 left-0 w-16 h-0.5 bg-gradient-to-r from-blue-400 to-transparent"></span>
                    </h3>
                    <ul class="space-y-3 mb-8">
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Web Development
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Mobile Apps
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Cloud Solutions
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            UI/UX Design
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            IT Consulting
                        </a></li>
                    </ul>

                    <!-- Contact Info -->
                    <div class="bg-white/5 rounded-xl p-6 backdrop-blur-sm border border-white/10">
                        <h4 class="font-semibold mb-4 text-white flex items-center">
                            <i class="fas fa-headset mr-2 text-blue-400"></i>
                            24/7 Support
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-envelope text-blue-400 w-4"></i>
                                <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-white transition-colors"><EMAIL></a>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-phone-alt text-blue-400 w-4"></i>
                                <a href="tel:+8801818898189" class="text-gray-300 hover:text-white transition-colors">+880 1818 898 189</a>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-clock text-blue-400 w-4"></i>
                                <span class="text-gray-300">Mon - Fri: 9:00 AM - 6:00 PM</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Enhanced Copyright Section -->
            <div class="border-t border-white/10 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="text-center md:text-left">
                        <p class="text-gray-300">
                            &copy; 2025 <span class="text-white font-bold bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">Pitor</span>. All rights reserved.
                        </p>
                        <p class="text-sm text-gray-400 mt-1">Empowering businesses through innovative digital solutions</p>
                    </div>
                    <div class="flex items-center space-x-6 text-sm text-gray-400">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-shield-alt text-green-400"></i>
                            <span>SSL Secured</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-award text-yellow-400"></i>
                            <span>ISO Certified</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-leaf text-green-400"></i>
                            <span>Eco Friendly</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button Script -->
    <script>
        // Back to top button functionality
        const backToTopButton = document.getElementById('backToTop');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.remove('opacity-0', 'invisible');
                backToTopButton.classList.add('opacity-100', 'visible');
            } else {
                backToTopButton.classList.remove('opacity-100', 'visible');
                backToTopButton.classList.add('opacity-0', 'invisible');
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>


    <!-- Contact Buttons -->
    <div class="fixed bottom-8 left-8 z-50 flex flex-col space-y-4">
        <!-- Toggle Button -->
        <div class="relative group">
            <button id="toggleChat"
                    class="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 text-white rounded-full shadow-lg hover:from-green-600 hover:to-teal-600 transition-all duration-300">
                <i class="fas fa-comments text-2xl transition-transform duration-300" id="toggleIcon"></i>
            </button>
            <span class="absolute left-full ml-4 top-1/2 transform -translate-y-1/2 whitespace-nowrap bg-black bg-opacity-75 text-white text-sm py-2 px-4 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                Chat with us
            </span>
        </div>

        <!-- Social Chat Links -->
        <div id="chatButtons" class="flex flex-col-reverse space-y-reverse space-y-4 scale-0 opacity-0 transition-all duration-300 origin-top absolute bottom-20">
            <!-- WhatsApp -->
            <a href="https://wa.me/+8801818898189"
               target="_blank"
               class="flex items-center justify-center w-14 h-14 bg-green-500 text-white rounded-full shadow-lg hover:bg-green-600 transition-colors">
                <i class="fab fa-whatsapp text-2xl"></i>
            </a>

            <!-- Telegram -->
            <a href="https://t.me/pitor"
               target="_blank"
               class="flex items-center justify-center w-14 h-14 bg-blue-400 text-white rounded-full shadow-lg hover:bg-blue-500 transition-colors">
                <i class="fab fa-telegram-plane text-2xl"></i>
            </a>

            <!-- Messenger -->
            <a href="https://m.me/pitor.net"
               target="_blank"
               class="flex items-center justify-center w-14 h-14 bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-600 transition-colors">
                <i class="fab fa-facebook-messenger text-2xl"></i>
            </a>

            <!-- Email -->
            <a href="#contact"
               class="flex items-center justify-center w-14 h-14 bg-red-500 text-white rounded-full shadow-lg hover:bg-red-600 transition-colors">
                <i class="fas fa-envelope text-2xl"></i>
            </a>



        </div>
    </div>
    <!-- Add this to your existing script section -->
    <script>
        // Chat buttons toggle functionality
        const toggleChat = document.getElementById('toggleChat');
        const chatButtons = document.getElementById('chatButtons');
        const toggleIcon = document.getElementById('toggleIcon');
        let isOpen = false;

        toggleChat.addEventListener('click', () => {
            isOpen = !isOpen;

            if (isOpen) {
                // Open state
                chatButtons.classList.remove('scale-0', 'opacity-0');
                chatButtons.classList.add('scale-100', 'opacity-100');
                toggleIcon.classList.add('rotate-45');
                toggleIcon.classList.remove('fa-comments');
                toggleIcon.classList.add('fa-times');
            } else {
                // Closed state
                chatButtons.classList.remove('scale-100', 'opacity-100');
                chatButtons.classList.add('scale-0', 'opacity-0');
                toggleIcon.classList.remove('rotate-45');
                toggleIcon.classList.remove('fa-times');
                toggleIcon.classList.add('fa-comments');
            }
        });
    </script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // For mobile dropdowns without Alpine.js
        document.querySelectorAll('#mobile-menu [x-data]').forEach(function(dropdown) {
            const button = dropdown.querySelector('button');
            const content = dropdown.querySelector('div[x-show]');

            // Initialize - make sure dropdowns are hidden
            content.style.display = 'none';

            button.addEventListener('click', function() {
                if (content.style.display === 'none') {
                    content.style.display = 'block';
                } else {
                    content.style.display = 'none';
                }
            });
        });
    });
</script>
</html>


    