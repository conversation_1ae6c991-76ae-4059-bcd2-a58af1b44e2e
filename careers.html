<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pitor - Digital Solutions Provider</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="icon" type="image/png" href="images/favicon.png">
    <!-- Social Media Sharing -->
    <meta property="og:image" content="images/social-share-image.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta name="twitter:image" content="images/social-share-image.png">
    <link rel="image_src" href="images/social-share-image.png">

    <!-- Custom Animations -->
    <style>
        @keyframes blob {
            0% { transform: translate(0px, 0px) scale(1); }
            33% { transform: translate(30px, -50px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
            100% { transform: translate(0px, 0px) scale(1); }
        }
        .animate-blob {
            animation: blob 7s infinite;
        }
        .animation-delay-2000 {
            animation-delay: 2s;
        }
        .animation-delay-4000 {
            animation-delay: 4s;
        }
    </style>

</head>

<body>
    <!-- Header -->
    <header class="bg-white shadow-md fixed w-full z-50">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-3">
                        <!-- Logo Mark -->
                        <img src="images/Logo.png" alt="Pitor Logo" class="w-18 h-10">
                        <!-- Logo Type -->
                        
                    </a>
                </div>
                
                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-blue-600">Home</a>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Services</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-laptop-code text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Web Development</div>
                                        <div class="text-sm text-gray-600">Custom website solutions</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-mobile-alt text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">Mobile Development</div>
                                        <div class="text-sm text-gray-600">iOS and Android applications</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-paint-brush text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">UI/UX Design</div>
                                        <div class="text-sm text-gray-600">User interface and experience</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-bullhorn text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Digital Marketing</div>
                                        <div class="text-sm text-gray-600">SEO and online advertising</div>
                                    </div>
                                </a>
                                <a href="#services" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-comments text-red-600"></i>
                                    <div>
                                        <div class="font-semibold">IT Consulting</div>
                                        <div class="text-sm text-gray-600">Expert technology advice</div>
                                    </div>
                                </a>
                                <div class="border-t border-gray-100 my-2"></div>
                                <a href="#services" class="flex items-center justify-between text-sm text-blue-600 hover:text-blue-700 p-2">
                                    <span>View All Services</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Solutions</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="e-commerce-solution.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-shopping-cart text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">E-Commerce Solution</div>
                                        <div class="text-sm text-gray-600">Complete online store management</div>
                                    </div>
                                </a>
                                <a href="crm-system.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-users text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">CRM System</div>
                                        <div class="text-sm text-gray-600">Customer relationship management</div>
                                    </div>
                                </a>
                                <a href="pos-system.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-cash-register text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">POS System</div>
                                        <div class="text-sm text-gray-600">Point of sale management</div>
                                    </div>
                                </a>
                                <a href="payment-gateway.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-credit-card text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Payment Gateway</div>
                                        <div class="text-sm text-gray-600">Secure payment processing</div>
                                    </div>
                                </a>

                                <a href="flight-booking.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-credit-card text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Flight Booking</div>
                                        <div class="text-sm text-gray-600">Secure Flight Ticket processing</div>
                                    </div>
                                </a>


                                <div class="border-t border-gray-100 my-2"></div>
                                <a href="solutions.html" class="flex items-center justify-between text-sm text-blue-600 hover:text-blue-700 p-2">
                                    <span>View All Solutions</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>

                               


                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Products</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="products.html?category=Management%20Systems" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-tasks text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Management Systems</div>
                                        <div class="text-sm text-gray-600">Business process automation</div>
                                    </div>
                                </a>
                                <a href="products.html?category=Financial%20Solutions" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-chart-line text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">Financial Solutions</div>
                                        <div class="text-sm text-gray-600">Payment and accounting tools</div>
                                    </div>
                                </a>
                                <a href="products.html?category=Booking%20Solutions" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-calendar-check text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">Booking Solutions</div>
                                        <div class="text-sm text-gray-600">Reservation and scheduling</div>
                                    </div>
                                </a>
                                <a href="products.html?category=Mobile%20Applications" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-mobile-alt text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Mobile Applications</div>
                                        <div class="text-sm text-gray-600">iOS and Android solutions</div>
                                    </div>
                                </a>
                                <a href="products.html?category=E-Commerce" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-shopping-cart text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">E-Commerce Solutions</div>
                                        <div class="text-sm text-gray-600">Online store management</div>
                                    </div>
                                </a>

                                <div class="border-t border-gray-100 my-2"></div>
                                <a href="products.html" class="flex items-center justify-between text-sm text-blue-600 hover:text-blue-700 p-2">
                                    <span>View All Products</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>

                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Hosting</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-server text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Shared Hosting</div>
                                        <div class="text-sm text-gray-600">Affordable hosting solutions</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-server text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">VPS Hosting</div>
                                        <div class="text-sm text-gray-600">Virtual private server hosting</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-hdd text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">Dedicated Servers</div>
                                        <div class="text-sm text-gray-600">Full server control and resources</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-cloud text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Cloud Hosting</div>
                                        <div class="text-sm text-gray-600">Scalable cloud infrastructure</div>
                                    </div>
                                </a>
                                <a href="web-hosting.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-globe text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">Domain Name Registration</div>
                                        <div class="text-sm text-gray-600">Secure your online identity</div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                            <span>Company</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-4 space-y-3">
                                <a href="about.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-info-circle text-blue-600"></i>
                                    <div>
                                        <div class="font-semibold">About</div>
                                        <div class="text-sm text-gray-600">Learn about our company</div>
                                    </div>
                                </a>
                                <a href="contact.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-envelope text-green-600"></i>
                                    <div>
                                        <div class="font-semibold">Contact Us</div>
                                        <div class="text-sm text-gray-600">Get in touch with us</div>
                                    </div>
                                </a>
                                <a href="careers.html" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-briefcase text-orange-600"></i>
                                    <div>
                                        <div class="font-semibold">Careers</div>
                                        <div class="text-sm text-gray-600">Join our team</div>
                                    </div>
                                </a>
                                <a href="about.html#team" class="flex items-center space-x-3 text-gray-700 hover:text-blue-600 hover:bg-gray-50 p-2 rounded-lg">
                                    <i class="fas fa-users text-purple-600"></i>
                                    <div>
                                        <div class="font-semibold">Our Team</div>
                                        <div class="text-sm text-gray-600">Meet our experts</div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                    <a href="login.html" class="text-gray-700 hover:text-blue-600 flex items-center space-x-1">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Login</span>
                    </a>
                </div>
    
                <!-- Mobile Menu Button -->
                <div class="md:hidden flex items-center space-x-4">
                    <a href="login.html" class="text-gray-700 hover:text-blue-600">
                        <i class="fas fa-sign-in-alt text-xl"></i>
                    </a>
                    <button id="mobile-menu-button" class="text-gray-700 hover:text-blue-600 focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </nav>
    
        <!-- Mobile Menu (Hidden by Default) -->
        <div id="mobile-menu" class="md:hidden hidden bg-white shadow-md">
            <div class="container mx-auto px-6 py-4">
                <a href="index.html" class="block text-gray-700 hover:text-blue-600 py-2">Home</a>
                <!-- Services Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Services</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">Web Development</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">Mobile Development</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">UI/UX Design</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">Digital Marketing</a>
                        <a href="#services" class="block text-gray-700 hover:text-blue-600 py-2">IT Consulting</a>
                        <a href="#services" class="block text-blue-600 hover:text-blue-700 py-2">View All Services</a>
                    </div>
                </div>
                <!-- Add Solutions Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Solutions</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="e-commerce-solution.html" class="block text-gray-700 hover:text-blue-600 py-2">E-Commerce Solution</a>
                        <a href="crm-system.html" class="block text-gray-700 hover:text-blue-600 py-2">CRM System</a>
                        <a href="pos-system.html" class="block text-gray-700 hover:text-blue-600 py-2">POS System</a>
                        <a href="payment-gateway.html" class="block text-gray-700 hover:text-blue-600 py-2">Payment Gateway</a>
                        <a href="solutions.html" class="block text-blue-600 hover:text-blue-700 py-2">View All Solutions</a>
                    </div>
                </div>
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Products</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="products.html?category=Management%20Systems" class="block text-gray-700 hover:text-blue-600 py-2">Management Systems</a>
                        <a href="products.html?category=Financial%20Solutions" class="block text-gray-700 hover:text-blue-600 py-2">Financial Solutions</a>
                        <a href="products.html?category=Booking%20Solutions" class="block text-gray-700 hover:text-blue-600 py-2">Booking Solutions</a>
                        <a href="products.html?category=Mobile%20Applications" class="block text-gray-700 hover:text-blue-600 py-2">Mobile Applications</a>
                        <a href="products.html?category=E-Commerce" class="block text-gray-700 hover:text-blue-600 py-2">E-Commerce Solutions</a>
                        <a href="products.html" class="block text-blue-600 hover:text-blue-700 py-2">View All Products</a>
                    </div>
                </div>
                <!-- Add Hosting Services Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Hosting</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Shared Hosting</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">VPS Hosting</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Dedicated Servers</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Cloud Hosting</a>
                        <a href="web-hosting.html" class="block text-gray-700 hover:text-blue-600 py-2">Domain Name Registration</a>
                    </div>
                </div>
                <a href="services.html" class="block text-gray-700 hover:text-blue-600 py-2">Services</a>
                <!-- Add Company Dropdown for Mobile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 hover:text-blue-600 py-2">
                        <span>Company</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div x-show="open" class="pl-4 space-y-2">
                        <a href="about.html" class="block text-gray-700 hover:text-blue-600 py-2">About</a>
                        <a href="contact.html" class="block text-gray-700 hover:text-blue-600 py-2">Contact Us</a>
                        <a href="careers.html" class="block text-gray-700 hover:text-blue-600 py-2">Careers</a>
                        <a href="about.html#team" class="block text-gray-700 hover:text-blue-600 py-2">Our Team</a>
                    </div>
                </div>
                <a href="login.html" class="block text-gray-700 hover:text-blue-600 py-2">Login</a>
            </div>
        </div>
    </header>
    
    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
        <!-- Background Image with Overlay -->
        <div class="absolute inset-0">
            <img src="images/careers-hero.jpg"
                 alt="Join Our Team"
                 class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-900/80 via-blue-800/70 to-indigo-900/80"></div>
        </div>

        <!-- Animated Background Elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-white/5 rounded-full animate-pulse"></div>
            <div class="absolute bottom-1/4 right-1/4 w-48 h-48 bg-blue-400/10 rounded-full animate-pulse animation-delay-2000"></div>
            <div class="absolute top-1/2 right-1/3 w-32 h-32 bg-indigo-400/10 rounded-full animate-pulse animation-delay-4000"></div>
        </div>

        <!-- Content -->
        <div class="relative z-10 container mx-auto px-6 text-center">
            <div class="max-w-4xl mx-auto">
                <!-- Badge -->
                <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 mb-8">
                    <i class="fas fa-briefcase text-blue-300 mr-2"></i>
                    <span class="text-white text-sm font-medium">Career Opportunities</span>
                </div>

                <!-- Main Heading -->
                <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
                    Join Our
                    <span class="bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">
                        Amazing Team
                    </span>
                </h1>

                <!-- Subtitle -->
                <p class="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed max-w-3xl mx-auto">
                    We're looking for passionate individuals to join our growing team.
                    Explore opportunities to grow your career with innovative projects and cutting-edge technology.
                </p>

                <!-- Feature Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                        <div class="bg-blue-500/20 rounded-lg p-3 w-fit mx-auto mb-4">
                            <i class="fas fa-rocket text-blue-300 text-2xl"></i>
                        </div>
                        <h3 class="text-white font-semibold mb-2">Innovation First</h3>
                        <p class="text-blue-100 text-sm">Work with cutting-edge technologies</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                        <div class="bg-indigo-500/20 rounded-lg p-3 w-fit mx-auto mb-4">
                            <i class="fas fa-users text-indigo-300 text-2xl"></i>
                        </div>
                        <h3 class="text-white font-semibold mb-2">Great Culture</h3>
                        <p class="text-blue-100 text-sm">Collaborative and supportive environment</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                        <div class="bg-purple-500/20 rounded-lg p-3 w-fit mx-auto mb-4">
                            <i class="fas fa-chart-line text-purple-300 text-2xl"></i>
                        </div>
                        <h3 class="text-white font-semibold mb-2">Career Growth</h3>
                        <p class="text-blue-100 text-sm">Continuous learning and development</p>
                    </div>
                </div>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#open-positions"
                       class="group bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        <span class="flex items-center justify-center">
                            View Open Positions
                            <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform"></i>
                        </span>
                    </a>
                    <a href="#why-join-us"
                       class="group bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-semibold border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:scale-105">
                        <span class="flex items-center justify-center">
                            Why Join Us?
                            <i class="fas fa-info-circle ml-2 transform group-hover:rotate-12 transition-transform"></i>
                        </span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
            <i class="fas fa-chevron-down text-2xl opacity-70"></i>
        </div>
    </section>

    <!-- Open Positions Section -->
    <section id="open-positions" class="bg-gray-50 py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-gray-800 text-center mb-12">Open Positions</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Frontend Developer</h3>
                    <p class="text-gray-600 mb-4">We're looking for a skilled frontend developer to create beautiful and responsive user interfaces.</p>
                    <div class="flex items-center space-x-4 text-gray-600">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Remote</span>
                    </div>
                    <div class="mt-6">
                        <a href="#" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300">Apply Now</a>
                    </div>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">UI/UX Designer</h3>
                    <p class="text-gray-600 mb-4">Join our design team to create intuitive and user-friendly experiences across our products.</p>
                    <div class="flex items-center space-x-4 text-gray-600">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>New York, NY</span>
                    </div>
                    <div class="mt-6">
                        <a href="#" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300">Apply Now</a>
                    </div>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Backend Developer</h3>
                    <p class="text-gray-600 mb-4">Seeking a backend developer to build robust server-side applications.</p>
                    <div class="flex items-center space-x-4 text-gray-600">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>San Francisco, CA</span>
                    </div>
                    <div class="mt-6">
                        <a href="#" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300">Apply Now</a>
                    </div>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Project Manager</h3>
                    <p class="text-gray-600 mb-4">Looking for a project manager to oversee and coordinate projects.</p>
                    <div class="flex items-center space-x-4 text-gray-600">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Remote</span>
                    </div>
                    <div class="mt-6">
                        <a href="#" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300">Apply Now</a>
                    </div>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Marketing Specialist</h3>
                    <p class="text-gray-600 mb-4">Join our marketing team to drive brand awareness and engagement.</p>
                    <div class="flex items-center space-x-4 text-gray-600">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Chicago, IL</span>
                    </div>
                    <div class="mt-6">
                        <a href="#" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300">Apply Now</a>
                    </div>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Data Analyst</h3>
                    <p class="text-gray-600 mb-4">Seeking a data analyst to interpret complex data sets and provide insights.</p>
                    <div class="flex items-center space-x-4 text-gray-600">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Remote</span>
                    </div>
                    <div class="mt-6">
                        <a href="#" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300">Apply Now</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Join Us Section -->
    <section id="why-join-us" class="py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-gray-800 text-center mb-12">Why Join Pitor?</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-lg shadow-md text-center">
                    <i class="fas fa-users text-blue-600 text-4xl mb-6"></i>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Collaborative Culture</h3>
                    <p class="text-gray-600">Work in a supportive environment that values teamwork and innovation.</p>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-md text-center">
                    <i class="fas fa-chart-line text-blue-600 text-4xl mb-6"></i>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Career Growth</h3>
                    <p class="text-gray-600">Access to training and opportunities to advance your skills and career.</p>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-md text-center">
                    <i class="fas fa-heart text-blue-600 text-4xl mb-6"></i>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Work-Life Balance</h3>
                    <p class="text-gray-600">Flexible work arrangements to support your personal and professional life.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-gray-800 text-center mb-12">Our Benefits</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-lg shadow-md text-center">
                    <i class="fas fa-briefcase text-blue-600 text-4xl mb-6"></i>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Competitive Salary</h3>
                    <p class="text-gray-600">We offer competitive compensation packages to attract top talent.</p>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-md text-center">
                    <i class="fas fa-stethoscope text-blue-600 text-4xl mb-6"></i>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Health Insurance</h3>
                    <p class="text-gray-600">Comprehensive health benefits for you and your family.</p>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-md text-center">
                    <i class="fas fa-coffee text-blue-600 text-4xl mb-6"></i>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Wellness Programs</h3>
                    <p class="text-gray-600">Access to wellness programs and mental health resources.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="bg-blue-600 py-20">
        <div class="container mx-auto px-6">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-white mb-6">Ready to Join Our Team?</h2>
                <p class="text-lg text-blue-100 mb-8">We're always looking for talented individuals to join our team. Don't see a position that matches your skills? Send us your resume anyway!</p>
                <a href="#" class="bg-white text-blue-600 px-8 py-3 rounded-lg hover:bg-blue-50 transition duration-300">Submit Your Resume</a>
            </div>
        </div>
    </section>



    <!-- Career -->
    <section class="pt-24 bg-gradient-to-br from-blue-50 to-indigo-50 overflow-hidden">
      
    </section>

<section class="py-16 bg-gray-50">
  <div class="container mx-auto px-4">
    <h2 class="text-3xl font-bold text-center mb-8">Careers at Pitor</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-semibold mb-4">Job Opportunities</h3>
        <p class="text-gray-600 mb-4">Explore exciting career opportunities in software development, design, and more.</p>
        <a href="#" class="text-blue-600 hover:text-blue-700">View Open Positions →</a>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-semibold mb-4">Our Culture</h3>
        <p class="text-gray-600 mb-4">We foster innovation, collaboration, and continuous learning in a supportive environment.</p>
        <a href="#" class="text-blue-600 hover:text-blue-700">Learn More →</a>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-semibold mb-4">Benefits</h3>
        <p class="text-gray-600 mb-4">We offer competitive salaries, health insurance, and professional development opportunities.</p>
        <a href="#" class="text-blue-600 hover:text-blue-700">See Benefits →</a>
      </div>
    </div>
  </div>
</section>
</body>


    <!-- Mobile Menu Dropdown (Hidden by default) -->
    <div class="md:hidden absolute top-full left-0 right-0 bg-white shadow-lg py-2 hidden">
        <a href="/" class="block px-6 py-2 text-gray-700 hover:bg-blue-50">Home</a>
        <a href="/products.html" class="block px-6 py-2 text-gray-700 hover:bg-blue-50">Products</a>
        <a href="/services.html" class="block px-6 py-2 text-gray-700 hover:bg-blue-50">Services</a>
        <a href="/about.html" class="block px-6 py-2 text-gray-700 hover:bg-blue-50">About</a>
        <a href="/contact.html" class="block px-6 py-2 text-gray-700 hover:bg-blue-50">Contact</a>
    </div>

    <!-- Add this before closing body tag -->
    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.querySelector('.md\\:hidden button');
        const mobileMenu = document.querySelector('.md\\:hidden.absolute');
        
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!mobileMenuButton.contains(e.target) && !mobileMenu.contains(e.target)) {
                mobileMenu.classList.add('hidden');
            }
        });
    </script>

    <!-- Contact Buttons -->
    <div class="fixed bottom-8 left-8 z-50 flex flex-col space-y-4">
        <!-- Toggle Button -->
        <div class="relative group">
            <button id="toggleChat"
                    class="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 text-white rounded-full shadow-lg hover:from-green-600 hover:to-teal-600 transition-all duration-300">
                <i class="fas fa-comments text-2xl transition-transform duration-300" id="toggleIcon"></i>
            </button>
            <span class="absolute left-full ml-4 top-1/2 transform -translate-y-1/2 whitespace-nowrap bg-black bg-opacity-75 text-white text-sm py-2 px-4 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                Chat with us
            </span>
        </div>

        <!-- Social Chat Links -->
        <div id="chatButtons" class="flex flex-col-reverse space-y-reverse space-y-4 scale-0 opacity-0 transition-all duration-300 origin-top absolute bottom-20">
            <!-- WhatsApp -->
            <a href="https://wa.me/+8801818898189"
               target="_blank"
               class="flex items-center justify-center w-14 h-14 bg-green-500 text-white rounded-full shadow-lg hover:bg-green-600 transition-colors">
                <i class="fab fa-whatsapp text-2xl"></i>
            </a>

            <!-- Telegram -->
            <a href="https://t.me/pitor"
               target="_blank"
               class="flex items-center justify-center w-14 h-14 bg-blue-400 text-white rounded-full shadow-lg hover:bg-blue-500 transition-colors">
                <i class="fab fa-telegram-plane text-2xl"></i>
            </a>

            <!-- Messenger -->
            <a href="https://m.me/pitor.net"
               target="_blank"
               class="flex items-center justify-center w-14 h-14 bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-600 transition-colors">
                <i class="fab fa-facebook-messenger text-2xl"></i>
            </a>

            <!-- Email -->
            <a href="#contact"
               class="flex items-center justify-center w-14 h-14 bg-red-500 text-white rounded-full shadow-lg hover:bg-red-600 transition-colors">
                <i class="fas fa-envelope text-2xl"></i>
            </a>



        </div>
    </div>
    <!-- Add this to your existing script section -->
    <script>
        // Chat buttons toggle functionality
        const toggleChat = document.getElementById('toggleChat');
        const chatButtons = document.getElementById('chatButtons');
        const toggleIcon = document.getElementById('toggleIcon');
        let isOpen = false;

        toggleChat.addEventListener('click', () => {
            isOpen = !isOpen;

            if (isOpen) {
                // Open state
                chatButtons.classList.remove('scale-0', 'opacity-0');
                chatButtons.classList.add('scale-100', 'opacity-100');
                toggleIcon.classList.add('rotate-45');
                toggleIcon.classList.remove('fa-comments');
                toggleIcon.classList.add('fa-times');
            } else {
                // Closed state
                chatButtons.classList.remove('scale-100', 'opacity-100');
                chatButtons.classList.add('scale-0', 'opacity-0');
                toggleIcon.classList.remove('rotate-45');
                toggleIcon.classList.remove('fa-times');
                toggleIcon.classList.add('fa-comments');
            }
        });
    </script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // For mobile dropdowns without Alpine.js
        document.querySelectorAll('#mobile-menu [x-data]').forEach(function(dropdown) {
            const button = dropdown.querySelector('button');
            const content = dropdown.querySelector('div[x-show]');

            // Initialize - make sure dropdowns are hidden
            content.style.display = 'none';

            button.addEventListener('click', function() {
                if (content.style.display === 'none') {
                    content.style.display = 'block';
                } else {
                    content.style.display = 'none';
                }
            });
        });
    });
</script>
       <button id="backToTop" class="fixed bottom-8 right-8 z-50 bg-blue-600 text-white rounded-full p-3 shadow-lg opacity-0 invisible transition-all duration-300 hover:bg-blue-700">
        <i class="fas fa-arrow-up text-lg"></i>
    </button>

    <!-- Simple Footer Divider -->
    <div class="border-t border-gray-200"></div>

    <!-- Footer -->
    <footer class="relative text-white overflow-hidden">
        <!-- Modern Gradient Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-slate-900 via-indigo-900 to-slate-800"></div>
        
        <!-- Animated Geometric Patterns -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <!-- Floating Shapes -->
            <div class="absolute -top-24 -right-24 w-80 h-80 bg-gradient-to-br from-blue-500/10 to-indigo-600/5 rounded-full animate-pulse"></div>
            <div class="absolute top-1/3 -left-32 w-64 h-64 bg-gradient-to-tr from-purple-500/8 to-blue-500/10 rounded-full animate-pulse animation-delay-700"></div>
            <div class="absolute -bottom-32 right-1/4 w-56 h-56 bg-gradient-to-tl from-indigo-500/10 to-purple-600/8 rounded-full animate-pulse animation-delay-1000"></div>
            
            <!-- Subtle Grid Pattern -->
            <div class="absolute inset-0 opacity-5">
                <div class="w-full h-full" style="background-image: radial-gradient(circle at 2px 2px, white 1px, transparent 0); background-size: 40px 40px;"></div>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="container mx-auto px-6 py-16 relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- About Column -->
                <div class="lg:col-span-2">
                    <div class="mb-8">
                        <img src="images/Logo.png" alt="Pitor Logo" class="w-40 h-auto mb-6 filter brightness-110">
                        <p class="text-gray-300 text-lg leading-relaxed mb-6">
                            Leading software development company providing innovative digital solutions for businesses worldwide. We transform ideas into powerful digital experiences.
                        </p>
                        
                        <!-- Enhanced Stats -->
                        <div class="grid grid-cols-3 gap-4 mb-8">
                            <div class="text-center p-4 bg-white/5 rounded-lg backdrop-blur-sm border border-white/10">
                                <div class="text-2xl font-bold text-blue-400">500+</div>
                                <div class="text-sm text-gray-400">Projects</div>
                            </div>
                            <div class="text-center p-4 bg-white/5 rounded-lg backdrop-blur-sm border border-white/10">
                                <div class="text-2xl font-bold text-green-400">200+</div>
                                <div class="text-sm text-gray-400">Clients</div>
                            </div>
                            <div class="text-center p-4 bg-white/5 rounded-lg backdrop-blur-sm border border-white/10">
                                <div class="text-2xl font-bold text-purple-400">5+</div>
                                <div class="text-sm text-gray-400">Years</div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Social Media -->
                    <div>
                        <h4 class="text-lg font-semibold mb-4 text-white">Connect With Us</h4>
                        <div class="flex space-x-4">
                            <a href="https://www.facebook.com/pitor.net/" target="_blank" 
                               class="group bg-white/10 hover:bg-blue-600 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-facebook-f text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-blue-400 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-twitter text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-blue-700 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-linkedin-in text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-gradient-to-tr hover:from-purple-600 hover:to-pink-500 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-instagram text-lg group-hover:text-white transition-colors"></i>
                            </a>
                            <a href="#" 
                               class="group bg-white/10 hover:bg-red-600 p-4 rounded-xl transition-all duration-300 transform hover:scale-110 hover:shadow-xl backdrop-blur-sm border border-white/20">
                                <i class="fab fa-youtube text-lg group-hover:text-white transition-colors"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div>
                    <h3 class="text-xl font-bold mb-6 relative text-white">
                        <span class="relative z-10">Quick Links</span>
                        <span class="absolute bottom-0 left-0 w-16 h-0.5 bg-gradient-to-r from-blue-400 to-transparent"></span>
                    </h3>
                    <ul class="space-y-3">
                        <li><a href="privacy-policy.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Privacy Policy
                        </a></li>
                        <li><a href="refund-policy.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Refund Policy
                        </a></li>
                        <li><a href="terms-of-services.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Terms of Service
                        </a></li>
                        <li><a href="about.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            About Us
                        </a></li>
                        <li><a href="contact.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Contact Us
                        </a></li>
                        <li><a href="careers.html" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Careers
                        </a></li>
                    </ul>
                </div>

                <!-- Services & Contact Column -->
                <div>
                    <h3 class="text-xl font-bold mb-6 relative text-white">
                        <span class="relative z-10">Our Services</span>
                        <span class="absolute bottom-0 left-0 w-16 h-0.5 bg-gradient-to-r from-blue-400 to-transparent"></span>
                    </h3>
                    <ul class="space-y-3 mb-8">
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Web Development
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Mobile Apps
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            Cloud Solutions
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            UI/UX Design
                        </a></li>
                        <li><a href="#" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
                            <i class="fas fa-chevron-right text-xs mr-3 text-blue-400 group-hover:translate-x-1 transition-transform"></i>
                            IT Consulting
                        </a></li>
                    </ul>

                    <!-- Contact Info -->
                    <div class="bg-white/5 rounded-xl p-6 backdrop-blur-sm border border-white/10">
                        <h4 class="font-semibold mb-4 text-white flex items-center">
                            <i class="fas fa-headset mr-2 text-blue-400"></i>
                            24/7 Support
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-envelope text-blue-400 w-4"></i>
                                <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-white transition-colors"><EMAIL></a>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-phone-alt text-blue-400 w-4"></i>
                                <a href="tel:+8801818898189" class="text-gray-300 hover:text-white transition-colors">+880 1818 898 189</a>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-clock text-blue-400 w-4"></i>
                                <span class="text-gray-300">Mon - Fri: 9:00 AM - 6:00 PM</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Enhanced Copyright Section -->
            <div class="border-t border-white/10 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="text-center md:text-left">
                        <p class="text-gray-300">
                            &copy; 2025 <span class="text-white font-bold bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">Pitor</span>. All rights reserved.
                        </p>
                        <p class="text-sm text-gray-400 mt-1">Empowering businesses through innovative digital solutions</p>
                    </div>
                    <div class="flex items-center space-x-6 text-sm text-gray-400">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-shield-alt text-green-400"></i>
                            <span>SSL Secured</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-award text-yellow-400"></i>
                            <span>ISO Certified</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-leaf text-green-400"></i>
                            <span>Eco Friendly</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button Script -->
    <script>
        // Back to top button functionality
        const backToTopButton = document.getElementById('backToTop');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.remove('opacity-0', 'invisible');
                backToTopButton.classList.add('opacity-100', 'visible');
            } else {
                backToTopButton.classList.remove('opacity-100', 'visible');
                backToTopButton.classList.add('opacity-0', 'invisible');
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</html>



    